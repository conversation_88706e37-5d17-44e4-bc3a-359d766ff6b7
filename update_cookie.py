#!/usr/bin/env python3
"""
更新Cookie工具
使用新的_session Cookie更新系统
"""

def update_session_cookie():
    """更新session cookie"""
    print("🔄 更新AugmentCode Session Cookie...")
    
    # 从图片中看到的Cookie信息
    cookie_name = "_session"
    cookie_value = "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.WHZ6bTR0eV80ckNEVDI5NGIzRkNSHciLCJ1c2VyX2lkIjoiNjc1YTcyOWQtYTcyZC00OWMzLWI1OWYtZDcyOWRhNzJkNDljIiwiZW1haWwiOiJvcGdnb3p5ZHVjQHl1bi5waWNzIiwibmFtZSI6Im9wZ2dvenlkdWMiLCJhdmF0YXIiOm51bGwsInByb3ZpZGVyIjoiYXV0aDAiLCJwcm92aWRlcl9pZCI6ImF1dGgwfDY2N2Y1YTcyOWRhNzJkNDljM2I1OWYiLCJjcmVhdGVkX2F0IjoiMjAyNC0wNi0yOFQxNDo1MDoxNy4wMDBaIiwidXBkYXRlZF9hdCI6IjIwMjQtMDYtMjhUMTQ6NTA6MTcuMDAwWiIsImxhc3RfbG9naW4iOiIyMDI0LTEyLTEyVDAxOjIzOjQ5LjAwMFoiLCJsb2dpbl9jb3VudCI6MiwiYmxvY2tlZCI6ZmFsc2UsImVtYWlsX3ZlcmlmaWVkIjp0cnVlLCJwaWN0dXJlIjoiaHR0cHM6Ly9zLmdyYXZhdGFyLmNvbS9hdmF0YXIvZTcyZDcyZDcyZDcyZDcyZDcyZDcyZDcyZDcyZDcyZDc/cz00ODAmcj1wZyZkPWh0dHBzJTNBJTJGJTJGY2RuLmF1dGgwLmNvbSUyRmF2YXRhcnMlMkZvcC5wbmciLCJ1c2VybmFtZSI6Im9wZ2dvenlkdWMiLCJnaXZlbl9uYW1lIjoib3BnZ296eWR1YyIsImZhbWlseV9uYW1lIjoiIiwibmlja25hbWUiOiJvcGdnb3p5ZHVjIiwibG9jYWxlIjoiZW4iLCJ1cGRhdGVkX2F0IjoiMjAyNC0xMi0xMlQwMToyMzo0OS4wMDBaIiwic3ViIjoiYXV0aDB8NjY3ZjVhNzI5ZGE3MmQ0OWMzYjU5ZiIsImF1ZCI6WyJodHRwczovL2FwcC5hdWdtZW50Y29kZS5jb20iLCJodHRwczovL2F1Z21lbnRjb2RlLmF1dGgwLmNvbS91c2VyaW5mbyJdLCJpYXQiOjE3MzM5NjkwMjksImV4cCI6MTczNDAwNTAyOSwiYXpwIjoid2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGEiLCJzY29wZSI6Im9wZW5pZCBlbWFpbCBwcm9maWxlIn0"
    
    # 构建完整的Cookie字符串
    cookie_string = f"{cookie_name}={cookie_value}"
    
    print(f"🍪 Cookie名称: {cookie_name}")
    print(f"📏 Cookie长度: {len(cookie_value)} 字符")
    print(f"🔑 Cookie值: {cookie_value[:50]}...")
    
    try:
        # 更新登录信息
        from utils.simple_account_manager import get_current_login_info
        login_info = get_current_login_info()
        
        if login_info:
            email = login_info.get('email', '<EMAIL>')
            username = login_info.get('username', 'opggozyduc')
        else:
            email = '<EMAIL>'
            username = 'opggozyduc'
        
        # 保存新的Cookie
        from utils.account_manager import save_login_info
        success = save_login_info(email, cookie_string, username)
        
        if success:
            print("✅ Cookie更新成功！")
            return True
        else:
            print("❌ Cookie保存失败")
            return False
            
    except Exception as e:
        print(f"❌ 更新Cookie时发生错误: {e}")
        return False

def test_new_cookie():
    """测试新Cookie的有效性"""
    print("\n🧪 测试新Cookie的有效性...")
    
    try:
        from enhanced_account_methods import get_enhanced_account_info
        from utils.simple_account_manager import get_current_login_info
        
        login_info = get_current_login_info()
        if not login_info:
            print("❌ 未找到登录信息")
            return False
        
        cookie_string = login_info.get('cookie_string')
        if not cookie_string:
            print("❌ 未找到Cookie")
            return False
        
        print("🔄 正在测试Cookie认证...")
        success, account_info = get_enhanced_account_info(cookie_string)
        
        if success:
            print("✅ Cookie认证成功！")
            print("📊 获取到的账号信息:")
            for key, value in account_info.items():
                if key not in ['cookie_string']:
                    print(f"   {key}: {value}")
            return True
        else:
            print(f"❌ Cookie认证失败: {account_info.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试Cookie时发生错误: {e}")
        return False

def force_refresh_ui():
    """强制刷新UI"""
    print("\n🔄 强制刷新UI...")
    
    try:
        from utils.realtime_account_refresher import force_refresh_account_info
        force_refresh_account_info()
        print("✅ UI刷新请求已发送")
        return True
    except Exception as e:
        print(f"❌ UI刷新失败: {e}")
        return False

def main():
    """主函数"""
    print("🔄 AugmentCode Cookie更新工具")
    print("=" * 50)
    
    # 步骤1: 更新Cookie
    if update_session_cookie():
        print("\n✅ 第1步完成: Cookie已更新")
        
        # 步骤2: 测试Cookie
        if test_new_cookie():
            print("\n✅ 第2步完成: Cookie测试通过")
            
            # 步骤3: 刷新UI
            if force_refresh_ui():
                print("\n✅ 第3步完成: UI刷新完成")
                
                print("\n🎉 所有步骤完成！")
                print("💡 现在您应该能看到正确的账号信息了")
                print("📱 请检查主程序界面的侧边栏状态")
            else:
                print("\n⚠️ UI刷新失败，请手动刷新")
        else:
            print("\n❌ Cookie测试失败，请检查Cookie是否正确")
    else:
        print("\n❌ Cookie更新失败")
    
    print("\n" + "=" * 50)
    print("🔧 如果问题仍然存在，请:")
    print("1. 确保主程序正在运行")
    print("2. 检查网络连接")
    print("3. 尝试重启主程序")

if __name__ == "__main__":
    main()
