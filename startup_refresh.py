#!/usr/bin/env python3
"""
启动时强制刷新账号信息
"""

import json
import os
from datetime import datetime

def force_refresh_on_startup():
    """启动时强制刷新"""
    try:
        # 读取真实数据
        cache_file = "data/account_cache.json"
        if os.path.exists(cache_file):
            with open(cache_file, "r", encoding="utf-8") as f:
                data = json.load(f)
            
            # 强制更新显示
            print(f"🔄 强制刷新账号信息: {data.get('email', 'N/A')}")
            print(f"📋 计划: {data.get('plan_name', 'N/A')}")
            print(f"💬 使用情况: {data.get('usage_count', 0)}/{data.get('usage_limit', 0)}")
            
            return True
    except Exception as e:
        print(f"❌ 启动刷新失败: {e}")
    
    return False

if __name__ == "__main__":
    force_refresh_on_startup()
