#!/usr/bin/env python3
"""
实时账号信息获取功能演示
"""

import time
import json
from datetime import datetime


def demo_realtime_account_info():
    """演示实时账号信息获取功能"""
    print("🎬 实时账号信息获取功能演示")
    print("=" * 60)
    
    # 检查登录状态
    try:
        from utils.simple_account_manager import get_current_login_info
        login_info = get_current_login_info()
        
        if not login_info:
            print("❌ 未检测到登录信息")
            print("💡 请先运行主程序并登录您的AugmentCode账号")
            return
        
        print(f"✅ 检测到登录账号: {login_info.get('email', 'Unknown')}")
        print(f"🕒 最后使用时间: {login_info.get('last_used', 'Unknown')}")
        
    except Exception as e:
        print(f"❌ 检查登录状态失败: {e}")
        return
    
    print("\n🚀 启动实时账号信息监控...")
    
    try:
        from utils.realtime_account_refresher import (
            get_realtime_refresher,
            add_account_info_callback,
            force_refresh_account_info
        )
        
        # 获取刷新器实例
        refresher = get_realtime_refresher()
        
        # 记录更新次数和历史
        update_count = 0
        update_history = []
        
        def account_update_callback(account_info):
            """账号信息更新回调"""
            nonlocal update_count, update_history
            update_count += 1
            
            current_time = datetime.now().strftime('%H:%M:%S')
            
            print(f"\n🔔 [{current_time}] 账号信息更新 #{update_count}")
            print("-" * 40)
            
            if account_info:
                # 显示关键信息
                email = account_info.get('email', 'N/A')
                plan_name = account_info.get('plan_name', 'N/A')
                remaining_count = account_info.get('remaining_count', 'N/A')
                usage_count = account_info.get('usage_count', 'N/A')
                usage_limit = account_info.get('usage_limit', 'N/A')
                reset_date = account_info.get('reset_date', 'N/A')
                refresh_time = account_info.get('refresh_time', 'N/A')
                
                print(f"📧 邮箱: {email}")
                print(f"📋 计划: {plan_name}")
                print(f"⚡ 剩余次数: {remaining_count}")
                
                if usage_limit and usage_limit != 'N/A' and usage_limit > 0:
                    usage_percent = (usage_count / usage_limit) * 100 if usage_count else 0
                    print(f"📊 使用情况: {usage_count}/{usage_limit} ({usage_percent:.1f}%)")
                
                if reset_date and reset_date != 'N/A':
                    print(f"🔄 重置日期: {reset_date}")
                
                print(f"🕒 更新时间: {refresh_time}")
                
                # 记录历史
                update_history.append({
                    'time': current_time,
                    'email': email,
                    'remaining_count': remaining_count,
                    'plan_name': plan_name
                })
                
            else:
                print("❌ 无账号信息")
            
            print("-" * 40)
        
        # 添加回调
        add_account_info_callback(account_update_callback)
        
        # 启动刷新器
        refresher.start()
        print("✅ 实时刷新器已启动")
        
        # 立即强制刷新一次
        print("🔄 执行初始刷新...")
        force_refresh_account_info()
        
        # 演示不同的刷新模式
        print("\n📖 演示说明:")
        print("1. 系统会每5分钟自动刷新账号信息")
        print("2. 您可以按 Enter 键手动强制刷新")
        print("3. 输入 'fast' 启用1分钟快速刷新模式")
        print("4. 输入 'quit' 退出演示")
        
        print("\n⏳ 等待账号信息更新...")
        print("💡 提示: 按 Enter 键可以手动刷新")
        
        # 交互式演示
        while True:
            try:
                user_input = input().strip().lower()
                
                if user_input == 'quit':
                    break
                elif user_input == 'fast':
                    print("⚡ 启用快速刷新模式（1分钟间隔，持续5分钟）")
                    refresher.enable_fast_refresh(duration_minutes=5)
                elif user_input == '' or user_input == 'refresh':
                    print("🔄 手动强制刷新...")
                    force_refresh_account_info()
                elif user_input == 'status':
                    # 显示当前状态
                    latest_info = refresher.get_latest_info()
                    last_update = refresher.get_last_update_time()
                    
                    print(f"\n📊 当前状态:")
                    print(f"   更新次数: {update_count}")
                    print(f"   最后更新: {last_update}")
                    
                    if latest_info:
                        print(f"   当前邮箱: {latest_info.get('email', 'N/A')}")
                        print(f"   剩余次数: {latest_info.get('remaining_count', 'N/A')}")
                    else:
                        print("   无最新信息")
                elif user_input == 'history':
                    # 显示更新历史
                    print(f"\n📜 更新历史 (最近{min(5, len(update_history))}次):")
                    for i, record in enumerate(update_history[-5:], 1):
                        print(f"   {i}. [{record['time']}] {record['email']} - 剩余: {record['remaining_count']}")
                elif user_input == 'help':
                    print("\n📖 可用命令:")
                    print("   Enter/refresh - 手动刷新")
                    print("   fast - 启用快速刷新模式")
                    print("   status - 显示当前状态")
                    print("   history - 显示更新历史")
                    print("   help - 显示帮助")
                    print("   quit - 退出演示")
                else:
                    print("❓ 未知命令，输入 'help' 查看可用命令")
                
            except KeyboardInterrupt:
                print("\n⏹️ 用户中断")
                break
            except EOFError:
                print("\n⏹️ 输入结束")
                break
        
        # 停止刷新器
        print("\n🛑 停止实时刷新器...")
        refresher.stop()
        
        # 显示演示总结
        print("\n" + "=" * 60)
        print("📋 演示总结:")
        print(f"   总更新次数: {update_count}")
        print(f"   演示时长: 约 {len(update_history)} 次更新")
        
        if update_history:
            first_update = update_history[0]['time']
            last_update = update_history[-1]['time']
            print(f"   时间范围: {first_update} - {last_update}")
        
        print("\n🎉 实时账号信息获取功能演示完成！")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


def demo_enhanced_methods():
    """演示增强账号方法"""
    print("\n🧪 增强账号方法演示")
    print("-" * 40)
    
    try:
        from enhanced_account_methods import get_enhanced_account_info
        from utils.simple_account_manager import get_current_login_info
        
        login_info = get_current_login_info()
        if not login_info:
            print("❌ 未找到登录信息")
            return
        
        cookie_string = login_info.get('cookie_string')
        if not cookie_string:
            print("❌ 未找到Cookie信息")
            return
        
        print("🔄 使用增强方法获取账号信息...")
        
        # 记录开始时间
        start_time = time.time()
        
        # 获取增强信息
        success, account_info = get_enhanced_account_info(cookie_string)
        
        # 记录结束时间
        end_time = time.time()
        duration = end_time - start_time
        
        if success:
            print(f"✅ 增强方法获取成功 (耗时: {duration:.2f}秒)")
            print("\n📊 详细账号信息:")
            
            for key, value in account_info.items():
                if key not in ['cookie_string', 'parse_error', 'post_process_error']:
                    print(f"   {key}: {value}")
        else:
            print(f"❌ 增强方法获取失败: {account_info.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ 增强方法演示失败: {e}")


def main():
    """主演示函数"""
    print("🎭 AugmentCode实时账号信息获取功能演示")
    print("=" * 60)
    
    # 演示增强方法
    demo_enhanced_methods()
    
    # 询问是否继续实时演示
    print("\n❓ 是否继续实时账号信息监控演示？")
    print("   这将启动实时刷新器并持续监控账号信息变化")
    print("   (输入 y/yes 继续，其他任意键退出)")
    
    try:
        user_choice = input("请选择: ").strip().lower()
        if user_choice in ['y', 'yes', '是', '1']:
            demo_realtime_account_info()
        else:
            print("👋 演示结束，感谢使用！")
    except KeyboardInterrupt:
        print("\n👋 演示结束，感谢使用！")


if __name__ == "__main__":
    main()
