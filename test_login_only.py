#!/usr/bin/env python3
"""
仅测试登录功能
"""

from utils.simple_account_manager import save_login_info, get_current_login_info, clear_login_info

def test_login_only():
    """仅测试登录功能"""
    print("🔍 测试登录功能")
    print("=" * 40)
    
    # 清除现有数据
    print("\n1. 清除现有数据:")
    clear_result = clear_login_info()
    print(f"清除结果: {'✅ 成功' if clear_result else '❌ 失败'}")
    
    # 检查当前状态
    print("\n2. 检查当前状态:")
    current_info = get_current_login_info()
    print(f"当前状态: {'❌ 无登录信息' if current_info is None else '✅ 有登录信息'}")
    
    # 保存登录信息
    print("\n3. 保存登录信息:")
    test_email = "<EMAIL>"
    test_cookie = "session=abc123; user=user123; auth=token456"
    test_username = "AugmentCode用户"
    
    save_result = save_login_info(test_email, test_cookie, test_username)
    print(f"保存结果: {'✅ 成功' if save_result else '❌ 失败'}")
    
    # 验证保存的信息
    print("\n4. 验证保存的信息:")
    saved_info = get_current_login_info()
    if saved_info:
        print("✅ 验证成功:")
        print(f"   邮箱: {saved_info.get('email')}")
        print(f"   用户名: {saved_info.get('username')}")
        print(f"   Cookie长度: {len(saved_info.get('cookie_string', ''))} 字符")
        print(f"   最后使用: {saved_info.get('last_used')}")
    else:
        print("❌ 验证失败")
    
    print("\n" + "=" * 40)
    print("🎯 登录功能测试完成")
    
    return saved_info is not None

if __name__ == "__main__":
    success = test_login_only()
    
    if success:
        print("\n✅ 登录功能正常工作！")
        print("现在您可以:")
        print("1. 启动GUI: python main.py")
        print("2. 进入'账号修改'页面")
        print("3. 看到已登录状态")
        print("4. 测试编辑和清除功能")
    else:
        print("\n❌ 登录功能有问题，请检查错误信息")
