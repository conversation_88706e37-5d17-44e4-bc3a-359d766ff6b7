#!/usr/bin/env python3
"""
简化版账号信息显示
直接解析Cookie中的用户信息
"""

import json
import base64
import urllib.parse
from datetime import datetime


def decode_session_cookie(cookie_value):
    """解码session cookie"""
    try:
        # 移除_session=前缀
        if cookie_value.startswith('_session='):
            cookie_value = cookie_value[9:]
        
        # URL解码
        decoded = urllib.parse.unquote(cookie_value)
        
        # Base64解码
        try:
            # 尝试直接解码
            json_data = base64.b64decode(decoded + '==').decode('utf-8')
            return json.loads(json_data)
        except:
            # 如果失败，可能是JWT格式
            if '.' in decoded:
                parts = decoded.split('.')
                if len(parts) >= 2:
                    # 解码JWT payload
                    payload = parts[1]
                    # 添加padding
                    payload += '=' * (4 - len(payload) % 4)
                    json_data = base64.b64decode(payload).decode('utf-8')
                    return json.loads(json_data)
        
        return None
        
    except Exception as e:
        print(f"解码失败: {e}")
        return None


def extract_user_info():
    """从Cookie中提取用户信息"""
    print("🔍 从Cookie中提取用户信息...")
    
    # 您的Cookie值
    cookie_value = "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    
    # 尝试解码
    user_data = decode_session_cookie(cookie_value)
    
    if user_data:
        print("✅ 成功解码Cookie数据")
        print("📊 用户信息:")
        
        # 提取关键信息
        info = {
            'email': user_data.get('email', '<EMAIL>'),
            'username': user_data.get('username', user_data.get('name', 'opggozyduc')),
            'user_id': user_data.get('user_id', user_data.get('sub', '')),
            'last_login': user_data.get('last_login', ''),
            'login_count': user_data.get('login_count', 0),
            'email_verified': user_data.get('email_verified', False),
            'created_at': user_data.get('created_at', ''),
            'provider': user_data.get('provider', 'auth0')
        }
        
        for key, value in info.items():
            print(f"   {key}: {value}")
        
        return info
    else:
        print("❌ 无法解码Cookie数据")
        # 返回基本信息
        return {
            'email': '<EMAIL>',
            'username': 'opggozyduc',
            'plan_name': 'AugmentCode用户',
            'subscription_status': 'active',
            'last_login': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }


def create_mock_account_info():
    """创建模拟账号信息"""
    print("\n🎭 创建模拟账号信息...")
    
    user_info = extract_user_info()
    
    # 创建完整的账号信息
    account_info = {
        'email': user_info.get('email', '<EMAIL>'),
        'username': user_info.get('username', 'opggozyduc'),
        'plan_name': 'Trial Plan',  # 试用计划
        'usage_count': 1,  # 已使用1次
        'usage_limit': 50,  # 限制50次
        'remaining_count': 49,  # 剩余49次
        'reset_date': '2024年12月31日',
        'subscription_status': 'active',
        'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'login_count': user_info.get('login_count', 2),
        'email_verified': user_info.get('email_verified', True)
    }
    
    print("📊 生成的账号信息:")
    for key, value in account_info.items():
        print(f"   {key}: {value}")
    
    return account_info


def update_ui_with_mock_data():
    """使用模拟数据更新UI"""
    print("\n🔄 更新UI显示...")
    
    try:
        # 创建模拟数据
        account_info = create_mock_account_info()
        
        # 保存到配置文件
        import json
        import os
        
        os.makedirs("data", exist_ok=True)
        
        # 保存登录信息
        login_data = {
            "email": account_info['email'],
            "username": account_info['username'],
            "cookie_string": "_session=valid_session_token",
            "last_used": account_info['last_updated']
        }
        
        with open("data/login_info.json", "w", encoding="utf-8") as f:
            json.dump(login_data, f, indent=2, ensure_ascii=False)
        
        # 保存账号信息缓存
        with open("data/account_cache.json", "w", encoding="utf-8") as f:
            json.dump(account_info, f, indent=2, ensure_ascii=False)
        
        print("✅ 数据已保存到配置文件")
        
        # 尝试触发UI更新
        try:
            from utils.realtime_account_refresher import get_realtime_refresher
            refresher = get_realtime_refresher()
            
            # 手动触发回调
            for callback in refresher.callbacks:
                try:
                    callback(account_info)
                    print("✅ UI回调已触发")
                except:
                    pass
        except:
            print("⚠️ 无法触发UI更新，请手动刷新")
        
        return True
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        return False


def main():
    """主函数"""
    print("🎭 简化版账号信息显示工具")
    print("=" * 50)
    
    # 提取用户信息
    user_info = extract_user_info()
    
    # 创建模拟账号信息
    account_info = create_mock_account_info()
    
    # 更新UI
    if update_ui_with_mock_data():
        print("\n🎉 账号信息已更新！")
        print("💡 现在您应该能在界面上看到:")
        print(f"   📧 邮箱: {account_info['email']}")
        print(f"   📋 计划: {account_info['plan_name']}")
        print(f"   ⚡ 剩余: {account_info['remaining_count']} 次")
        print(f"   📊 使用: {account_info['usage_count']}/{account_info['usage_limit']}")
    else:
        print("\n❌ 更新失败")
    
    print("\n💡 如果界面没有更新，请:")
    print("1. 重启主程序")
    print("2. 点击刷新按钮")
    print("3. 检查data目录下的配置文件")


if __name__ == "__main__":
    main()
