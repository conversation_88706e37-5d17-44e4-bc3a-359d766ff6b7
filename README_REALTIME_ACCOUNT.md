# 实时账号信息获取功能

## 概述

本项目新增了实时获取AugmentCode账号信息的功能，解决了"没有实时获取账号信息"的问题。该功能包括：

- 🔄 **实时刷新**: 自动定期获取最新的账号信息
- ⚡ **快速响应**: 支持手动强制刷新和快速刷新模式
- 🎯 **多数据源**: 并行从多个页面获取信息，提高成功率
- 💾 **智能缓存**: 避免频繁请求，提高性能
- 🔔 **实时通知**: UI界面实时更新显示最新信息

## 功能特性

### 1. 增强账号信息获取

- **多页面并行抓取**: 同时从订阅页面、仪表板、个人资料等多个页面获取信息
- **智能HTML解析**: 使用多种正则表达式模式匹配不同格式的数据
- **容错处理**: 单个数据源失败不影响整体功能
- **数据验证**: 自动验证和清理获取的数据

### 2. 实时刷新机制

- **自动刷新**: 默认每5分钟自动刷新一次
- **快速刷新**: 支持临时启用1分钟间隔的快速刷新
- **强制刷新**: 支持立即手动刷新
- **错误重试**: 自动重试失败的请求，超过阈值后停止

### 3. UI集成

- **侧边栏实时显示**: 主界面侧边栏实时显示账号状态
- **详细信息页面**: 账号状态页面显示完整的账号信息
- **加载状态**: 显示数据获取进度和状态
- **错误提示**: 友好的错误信息显示

## 安装和配置

### 1. 安装依赖

运行安装脚本来安装必要的依赖：

```bash
python install_enhanced_dependencies.py
```

该脚本会自动安装以下基础包：
- `requests` - HTTP请求库
- `beautifulsoup4` - HTML解析库
- `lxml` - XML/HTML解析器
- `html5lib` - HTML5解析器
- `urllib3` - HTTP客户端
- `certifi` - SSL证书

可选的高级包（提供更好的抓取能力）：
- `selenium` - 浏览器自动化
- `undetected-chromedriver` - 反检测Chrome驱动
- `fake-useragent` - 随机User-Agent
- `cloudscraper` - 绕过Cloudflare保护

### 2. 启动应用

```bash
python main.py
```

应用启动时会自动初始化实时刷新器。

## 使用方法

### 1. 基本使用

1. **登录账号**: 在账号状态页面登录您的AugmentCode账号
2. **自动刷新**: 系统会自动开始实时获取账号信息
3. **查看信息**: 在侧边栏和账号状态页面查看实时信息

### 2. 手动操作

- **强制刷新**: 点击侧边栏的"🔄 刷新"按钮
- **快速刷新**: 在账号状态页面点击"🔄 刷新状态"启用5分钟快速刷新
- **查看详情**: 切换到"账号修改"页面查看详细信息

### 3. 编程接口

```python
from utils.realtime_account_refresher import (
    get_realtime_refresher,
    add_account_info_callback,
    force_refresh_account_info
)

# 获取刷新器实例
refresher = get_realtime_refresher()

# 添加回调函数
def on_account_updated(account_info):
    print(f"账号信息已更新: {account_info}")

add_account_info_callback(on_account_updated)

# 启动实时刷新
refresher.start()

# 强制刷新
force_refresh_account_info()
```

## 测试功能

运行测试脚本来验证功能是否正常：

```bash
python test_realtime_account_info.py
```

测试脚本会检查：
- 原有订阅检查器功能
- 增强账号方法功能
- 实时刷新器功能

## 技术架构

### 1. 核心组件

```
enhanced_account_methods.py          # 增强账号信息获取
├── EnhancedAccountChecker          # 主要检查器类
├── 并行数据获取                     # 多线程并行抓取
├── HTML解析方法                     # 智能解析网页内容
└── 缓存机制                        # 减少重复请求

utils/realtime_account_refresher.py  # 实时刷新器
├── RealtimeAccountRefresher        # 刷新器主类
├── 后台刷新线程                     # 定期自动刷新
├── 回调通知机制                     # UI更新通知
└── 错误处理和重试                   # 容错机制
```

### 2. 数据流

```
登录信息 → Cookie提取 → 并行网页抓取 → 数据解析 → 信息合并 → 缓存存储 → UI更新
```

### 3. 刷新策略

- **正常模式**: 5分钟间隔自动刷新
- **快速模式**: 1分钟间隔刷新（临时启用）
- **强制刷新**: 立即刷新（用户触发）
- **错误重试**: 失败后30秒重试，最多5次

## 获取的信息

系统可以获取以下账号信息：

- **基本信息**:
  - 邮箱地址
  - 用户名
  - 登录时间

- **订阅信息**:
  - 订阅计划名称
  - 使用次数
  - 总限制次数
  - 剩余次数
  - 重置/到期日期
  - 订阅状态

- **账单信息**:
  - 下次计费日期
  - 付费金额
  - 付费方式
  - 账单状态

## 故障排除

### 1. 常见问题

**Q: 实时刷新器启动失败**
A: 检查是否已安装必要的依赖，运行 `python install_enhanced_dependencies.py`

**Q: 无法获取账号信息**
A: 检查Cookie是否有效，尝试重新登录

**Q: 网络请求失败**
A: 检查网络连接，确认可以访问 `https://app.augmentcode.com`

**Q: 信息显示不完整**
A: 某些信息可能需要特定的订阅计划才能显示

### 2. 调试方法

1. **查看控制台输出**: 运行时会输出详细的调试信息
2. **运行测试脚本**: 使用 `test_realtime_account_info.py` 诊断问题
3. **检查日志**: 查看应用运行时的错误信息

### 3. 性能优化

- **缓存时间**: 默认5分钟缓存，可根据需要调整
- **并发数量**: 默认4个并发请求，可根据网络情况调整
- **超时设置**: 默认15秒超时，网络较慢时可适当增加

## 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 新增实时账号信息获取功能
- ✨ 新增增强账号方法模块
- ✨ 新增实时刷新器
- ✨ 集成UI实时更新
- ✨ 新增测试和安装脚本

## 贡献

欢迎提交Issue和Pull Request来改进这个功能。

## 许可证

本项目遵循原项目的许可证。
