#!/usr/bin/env python3
"""
测试网络修复效果
"""

import time
from datetime import datetime

def test_basic_connection():
    """测试基本连接"""
    print("🧪 测试基本网络连接...")
    
    try:
        import requests
        response = requests.get("https://httpbin.org/get", timeout=10)
        if response.status_code == 200:
            print("✅ 基本网络连接正常")
            return True
        else:
            print(f"⚠️ 基本网络连接异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 基本网络连接失败: {e}")
        return False

def test_advanced_network_handler():
    """测试高级网络处理器"""
    print("\n🧪 测试高级网络处理器...")
    
    try:
        from advanced_network_handler import get_network_handler
        
        handler = get_network_handler()
        print("✅ 高级网络处理器创建成功")
        
        # 测试连接
        results = handler.test_connection()
        print(f"📊 连接测试结果:")
        print(f"   基本测试: {'✅' if results['basic_test'] else '❌'}")
        print(f"   AugmentCode测试: {'✅' if results['augment_test'] else '❌'}")
        
        return results['basic_test'] and results['augment_test']
        
    except Exception as e:
        print(f"❌ 高级网络处理器测试失败: {e}")
        return False

def test_enhanced_account_methods():
    """测试增强账号方法"""
    print("\n🧪 测试增强账号方法...")
    
    try:
        from enhanced_account_methods import get_enhanced_account_info
        from utils.simple_account_manager import get_current_login_info
        
        login_info = get_current_login_info()
        if not login_info:
            print("❌ 未找到登录信息")
            return False
        
        cookie_string = login_info.get('cookie_string')
        if not cookie_string:
            print("❌ 未找到Cookie信息")
            return False
        
        print(f"📧 测试账号: {login_info.get('email', 'Unknown')}")
        print("🔄 正在获取账号信息...")
        
        start_time = time.time()
        success, account_info = get_enhanced_account_info(cookie_string)
        end_time = time.time()
        
        duration = end_time - start_time
        
        if success:
            print(f"✅ 账号信息获取成功 (耗时: {duration:.2f}秒)")
            print("📊 获取到的信息:")
            for key, value in account_info.items():
                if key not in ['cookie_string', 'parse_error']:
                    print(f"   {key}: {value}")
            return True
        else:
            print(f"❌ 账号信息获取失败: {account_info.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ 增强账号方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_network_stability():
    """测试网络稳定性"""
    print("\n🧪 测试网络稳定性（连续5次请求）...")
    
    try:
        from advanced_network_handler import get_network_handler
        from utils.simple_account_manager import get_current_login_info
        
        login_info = get_current_login_info()
        if not login_info:
            print("❌ 未找到登录信息")
            return False
        
        handler = get_network_handler()
        handler.setup_cookies(login_info.get('cookie_string'))
        
        success_count = 0
        total_time = 0
        
        for i in range(5):
            print(f"🔄 第 {i+1} 次请求...")
            
            start_time = time.time()
            content = handler.get_page_content("/dashboard")
            end_time = time.time()
            
            request_time = end_time - start_time
            total_time += request_time
            
            if content:
                success_count += 1
                print(f"✅ 请求成功 (耗时: {request_time:.2f}秒)")
            else:
                print(f"❌ 请求失败 (耗时: {request_time:.2f}秒)")
            
            # 等待间隔
            if i < 4:  # 最后一次不等待
                time.sleep(3)
        
        success_rate = success_count / 5
        avg_time = total_time / 5
        
        print(f"\n📊 稳定性测试结果:")
        print(f"   成功率: {success_rate:.1%} ({success_count}/5)")
        print(f"   平均耗时: {avg_time:.2f}秒")
        
        return success_rate >= 0.6  # 60%以上成功率认为通过
        
    except Exception as e:
        print(f"❌ 网络稳定性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试网络修复效果")
    print("=" * 50)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 执行测试
    test_results = []
    
    # 测试1: 基本连接
    result1 = test_basic_connection()
    test_results.append(("基本网络连接", result1))
    
    # 测试2: 高级网络处理器
    result2 = test_advanced_network_handler()
    test_results.append(("高级网络处理器", result2))
    
    # 测试3: 增强账号方法
    result3 = test_enhanced_account_methods()
    test_results.append(("增强账号方法", result3))
    
    # 测试4: 网络稳定性
    result4 = test_network_stability()
    test_results.append(("网络稳定性", result4))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📋 网络修复效果测试结果:")
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    passed_count = sum(1 for _, result in test_results if result)
    total_count = len(test_results)
    
    print(f"\n🎯 总体结果: {passed_count}/{total_count} 项测试通过")
    
    if passed_count == total_count:
        print("🎉 所有测试通过！网络问题已解决")
    elif passed_count >= total_count * 0.75:
        print("✅ 大部分测试通过，网络问题显著改善")
    elif passed_count >= total_count * 0.5:
        print("⚠️ 部分测试通过，网络问题有所改善")
    else:
        print("❌ 多数测试失败，网络问题仍需进一步解决")
    
    # 提供建议
    print("\n💡 建议:")
    if not test_results[0][1]:  # 基本连接失败
        print("   - 检查网络连接和防火墙设置")
    if not test_results[1][1]:  # 高级处理器失败
        print("   - 检查代码依赖和配置")
    if not test_results[2][1]:  # 账号方法失败
        print("   - 检查Cookie有效性，可能需要重新登录")
    if not test_results[3][1]:  # 稳定性测试失败
        print("   - 考虑使用代理IP或增加请求间隔")

if __name__ == "__main__":
    main()
