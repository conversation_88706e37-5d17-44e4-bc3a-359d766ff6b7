#!/usr/bin/env python3
"""
增强账号方法模块
提供实时获取AugmentCode账号信息的高级功能
"""

import requests
import json
import re
import time
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple, List
import urllib.parse
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading


class EnhancedAccountChecker:
    """增强账号检查器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://app.augmentcode.com"
        self.timeout = 15
        self.max_retries = 3
        self.cache = {}
        self.cache_duration = 300  # 5分钟缓存
        self.request_delay = 2  # 请求间隔2秒

        # 设置更真实的请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Microsoft Edge";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        })

        # 配置会话
        self.session.verify = True
        self.session.max_redirects = 5
    
    def setup_cookies(self, cookie_string: str) -> bool:
        """设置Cookie"""
        try:
            cookies = self._parse_cookie_string(cookie_string)
            if not cookies:
                print("❌ Cookie解析失败")
                return False

            # 清除现有cookies
            self.session.cookies.clear()

            # 设置cookies，支持多个域名
            domains = ['.augmentcode.com', 'app.augmentcode.com', 'augmentcode.com']

            for key, value in cookies.items():
                for domain in domains:
                    try:
                        self.session.cookies.set(key, value, domain=domain)
                    except:
                        pass

            print(f"✅ 已设置 {len(cookies)} 个Cookie")

            # 验证Cookie是否有效
            return self._validate_cookies()

        except Exception as e:
            print(f"❌ Cookie设置失败: {e}")
            return False

    def _validate_cookies(self) -> bool:
        """验证Cookie是否有效"""
        try:
            # 尝试访问一个简单的页面来验证Cookie
            test_url = f"{self.base_url}/dashboard"
            response = self.session.head(test_url, timeout=5)

            # 如果返回200或302（重定向），说明Cookie可能有效
            if response.status_code in [200, 302]:
                print("✅ Cookie验证成功")
                return True
            elif response.status_code == 401:
                print("❌ Cookie已过期或无效")
                return False
            else:
                print(f"⚠️ Cookie验证返回状态码: {response.status_code}")
                return True  # 其他状态码也认为可能有效

        except Exception as e:
            print(f"⚠️ Cookie验证失败: {e}")
            return True  # 验证失败不代表Cookie无效
    
    def _parse_cookie_string(self, cookie_string: str) -> Dict[str, str]:
        """解析Cookie字符串"""
        cookies = {}
        
        try:
            if ';' in cookie_string:
                parts = cookie_string.split(';')
                for part in parts:
                    part = part.strip()
                    if '=' in part:
                        key, value = part.split('=', 1)
                        cookies[key.strip()] = value.strip()
            elif '=' in cookie_string:
                key, value = cookie_string.split('=', 1)
                cookies[key.strip()] = value.strip()
            else:
                token = cookie_string.strip()
                possible_keys = [
                    'auth_token', 'access_token', 'session_token', 
                    'jwt_token', 'bearer_token', 'api_token',
                    'augment_token', 'user_token'
                ]
                for key in possible_keys:
                    cookies[key] = token
        except Exception:
            pass
            
        return cookies
    
    def get_enhanced_account_info(self, cookie_string: str) -> Tuple[bool, Dict]:
        """获取增强的账号信息"""
        # 检查缓存
        cache_key = f"account_info_{hash(cookie_string)}"
        if cache_key in self.cache:
            cache_time, cache_data = self.cache[cache_key]
            if time.time() - cache_time < self.cache_duration:
                return True, cache_data
        
        # 设置Cookie
        if not self.setup_cookies(cookie_string):
            return False, {'error': 'Cookie设置失败'}
        
        # 获取账号信息
        account_info = self._fetch_account_info_simple()
        
        if account_info:
            # 缓存结果
            self.cache[cache_key] = (time.time(), account_info)
            return True, account_info
        else:
            return False, {'error': '无法获取账号信息'}
    
    def _fetch_account_info_simple(self) -> Optional[Dict]:
        """简化的账号信息获取"""
        result = {
            'plan_name': 'Cookie登录',
            'usage_count': 0,
            'usage_limit': 0,
            'remaining_count': 0,
            'reset_date': '',
            'subscription_status': 'active',
            'email': '',
            'username': '',
            'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 尝试获取订阅页面
        try:
            subscription_result = self._fetch_subscription_page()
            if subscription_result:
                result.update(subscription_result)
                print("✅ 订阅页面数据获取成功")
                return result
        except Exception as e:
            print(f"❌ 订阅页面获取失败: {e}")
        
        # 如果订阅页面失败，尝试仪表板
        try:
            dashboard_result = self._fetch_dashboard_page()
            if dashboard_result:
                result.update(dashboard_result)
                print("✅ 仪表板数据获取成功")
                return result
        except Exception as e:
            print(f"❌ 仪表板获取失败: {e}")
        
        # 如果都失败，返回基本信息
        print("⚠️ 使用基本信息")
        return result
    
    def _fetch_subscription_page(self) -> Optional[Dict]:
        """获取订阅页面信息"""
        return self._fetch_page_with_retry("/dashboard/subscription", "订阅页面", self._parse_subscription_html)

    def _fetch_dashboard_page(self) -> Optional[Dict]:
        """获取仪表板页面信息"""
        return self._fetch_page_with_retry("/dashboard", "仪表板页面", self._parse_dashboard_html)

    def _fetch_page_with_retry(self, path: str, page_name: str, parser_func) -> Optional[Dict]:
        """带重试机制的页面获取"""
        url = f"{self.base_url}{path}"

        for attempt in range(self.max_retries):
            try:
                # 添加请求间隔，避免被限制
                if attempt > 0:
                    time.sleep(self.request_delay * attempt)

                # 更新Referer头
                self.session.headers.update({
                    'Referer': self.base_url + "/dashboard" if path != "/dashboard" else self.base_url
                })

                print(f"🔄 尝试获取{page_name} (第{attempt + 1}次)")

                response = self.session.get(url, timeout=self.timeout, allow_redirects=True)

                if response.status_code == 200:
                    print(f"✅ {page_name}获取成功")
                    return parser_func(response.text)
                elif response.status_code == 403:
                    print(f"❌ {page_name}访问被拒绝 (403)")
                    break  # 403错误不重试
                elif response.status_code == 401:
                    print(f"❌ {page_name}需要重新登录 (401)")
                    break  # 401错误不重试
                else:
                    print(f"⚠️ {page_name}返回状态码: {response.status_code}")

            except requests.exceptions.ConnectionError as e:
                print(f"❌ {page_name}连接错误 (第{attempt + 1}次): {e}")
                if "10054" in str(e):
                    print("💡 检测到连接被重置，可能是反爬虫机制")
            except requests.exceptions.Timeout as e:
                print(f"⏰ {page_name}请求超时 (第{attempt + 1}次): {e}")
            except requests.exceptions.RequestException as e:
                print(f"❌ {page_name}请求异常 (第{attempt + 1}次): {e}")
            except Exception as e:
                print(f"❌ {page_name}未知错误 (第{attempt + 1}次): {e}")

        print(f"❌ {page_name}获取失败，已尝试{self.max_retries}次")
        return None
    
    def _parse_subscription_html(self, html: str) -> Dict:
        """解析订阅页面HTML"""
        result = {}
        
        try:
            # 查找剩余次数
            available_patterns = [
                r'(\d+(?:\.\d+)?)\s+available',
                r'available["\']?\s*:\s*(\d+(?:\.\d+)?)',
                r'remaining["\']?\s*:\s*(\d+(?:\.\d+)?)'
            ]
            
            for pattern in available_patterns:
                match = re.search(pattern, html, re.IGNORECASE)
                if match:
                    remaining_value = float(match.group(1))
                    result['remaining_count'] = int(remaining_value) if remaining_value.is_integer() else remaining_value
                    break
            
            # 查找使用情况
            used_patterns = [
                r'Used\s+(\d+)\s+of\s+(\d+)\s+this\s+month',
                r'(\d+)\s*/\s*(\d+)\s*(?:messages?|requests?|uses?)'
            ]
            
            for pattern in used_patterns:
                match = re.search(pattern, html, re.IGNORECASE)
                if match:
                    result['usage_count'] = int(match.group(1))
                    result['usage_limit'] = int(match.group(2))
                    if 'remaining_count' not in result:
                        result['remaining_count'] = result['usage_limit'] - result['usage_count']
                    break
            
            # 查找计划名称
            plan_patterns = [
                r'(Trial\s+Plan)',
                r'(Pro\s+Plan)',
                r'(Community\s+Plan)',
                r'plan["\']?\s*:\s*["\']([^"\']+)["\']'
            ]
            
            for pattern in plan_patterns:
                match = re.search(pattern, html, re.IGNORECASE)
                if match:
                    result['plan_name'] = match.group(1).strip()
                    break
            
            # 查找日期
            date_patterns = [
                r'(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d+,\s+\d{4}',
                r'(\d{4}-\d{2}-\d{2})'
            ]
            
            for pattern in date_patterns:
                match = re.search(pattern, html, re.IGNORECASE)
                if match:
                    result['reset_date'] = match.group(0).strip()
                    break
            
        except Exception as e:
            result['parse_error'] = str(e)
        
        return result
    
    def _parse_dashboard_html(self, html: str) -> Dict:
        """解析仪表板页面HTML"""
        result = {}
        
        try:
            # 查找用户名
            username_patterns = [
                r'Welcome,?\s+([^<,!]+)',
                r'Hello,?\s+([^<,!]+)',
                r'username["\']?\s*:\s*["\']([^"\']+)["\']'
            ]
            
            for pattern in username_patterns:
                match = re.search(pattern, html, re.IGNORECASE)
                if match:
                    username = match.group(1).strip()
                    if len(username) < 50:
                        result['username'] = username
                        break
            
            # 查找使用统计
            stats_patterns = [
                r'(\d+)\s*(?:messages?|requests?|queries?)\s*(?:used|sent|made)',
                r'usage["\']?\s*:\s*(\d+)'
            ]
            
            for pattern in stats_patterns:
                match = re.search(pattern, html, re.IGNORECASE)
                if match:
                    result['usage_count'] = int(match.group(1))
                    break
            
        except Exception as e:
            result['parse_error'] = str(e)
        
        return result


# 全局实例
_enhanced_checker = None


def get_enhanced_account_checker() -> EnhancedAccountChecker:
    """获取增强账号检查器实例"""
    global _enhanced_checker
    if _enhanced_checker is None:
        _enhanced_checker = EnhancedAccountChecker()
    return _enhanced_checker


def get_enhanced_account_info(cookie_string: str) -> Tuple[bool, Dict]:
    """获取增强的账号信息（便捷函数）"""
    checker = get_enhanced_account_checker()
    return checker.get_enhanced_account_info(cookie_string)
