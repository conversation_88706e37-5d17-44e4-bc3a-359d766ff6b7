#!/usr/bin/env python3
"""
增强账号方法模块
提供实时获取AugmentCode账号信息的高级功能
"""

import requests
import json
import re
import time
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple, List
import urllib.parse
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading


class EnhancedAccountChecker:
    """增强账号检查器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://app.augmentcode.com"
        self.timeout = 10
        self.max_retries = 2
        self.cache = {}
        self.cache_duration = 300  # 5分钟缓存
        
        # 设置默认请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        })
    
    def setup_cookies(self, cookie_string: str) -> bool:
        """设置Cookie"""
        try:
            cookies = self._parse_cookie_string(cookie_string)
            if not cookies:
                return False
            
            self.session.cookies.clear()
            for key, value in cookies.items():
                self.session.cookies.set(key, value, domain='.augmentcode.com')
            
            return True
        except Exception:
            return False
    
    def _parse_cookie_string(self, cookie_string: str) -> Dict[str, str]:
        """解析Cookie字符串"""
        cookies = {}
        
        try:
            if ';' in cookie_string:
                parts = cookie_string.split(';')
                for part in parts:
                    part = part.strip()
                    if '=' in part:
                        key, value = part.split('=', 1)
                        cookies[key.strip()] = value.strip()
            elif '=' in cookie_string:
                key, value = cookie_string.split('=', 1)
                cookies[key.strip()] = value.strip()
            else:
                token = cookie_string.strip()
                possible_keys = [
                    'auth_token', 'access_token', 'session_token', 
                    'jwt_token', 'bearer_token', 'api_token',
                    'augment_token', 'user_token'
                ]
                for key in possible_keys:
                    cookies[key] = token
        except Exception:
            pass
            
        return cookies
    
    def get_enhanced_account_info(self, cookie_string: str) -> Tuple[bool, Dict]:
        """获取增强的账号信息"""
        # 检查缓存
        cache_key = f"account_info_{hash(cookie_string)}"
        if cache_key in self.cache:
            cache_time, cache_data = self.cache[cache_key]
            if time.time() - cache_time < self.cache_duration:
                return True, cache_data
        
        # 设置Cookie
        if not self.setup_cookies(cookie_string):
            return False, {'error': 'Cookie设置失败'}
        
        # 获取账号信息
        account_info = self._fetch_account_info_simple()
        
        if account_info:
            # 缓存结果
            self.cache[cache_key] = (time.time(), account_info)
            return True, account_info
        else:
            return False, {'error': '无法获取账号信息'}
    
    def _fetch_account_info_simple(self) -> Optional[Dict]:
        """简化的账号信息获取"""
        result = {
            'plan_name': 'Cookie登录',
            'usage_count': 0,
            'usage_limit': 0,
            'remaining_count': 0,
            'reset_date': '',
            'subscription_status': 'active',
            'email': '',
            'username': '',
            'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 尝试获取订阅页面
        try:
            subscription_result = self._fetch_subscription_page()
            if subscription_result:
                result.update(subscription_result)
                print("✅ 订阅页面数据获取成功")
                return result
        except Exception as e:
            print(f"❌ 订阅页面获取失败: {e}")
        
        # 如果订阅页面失败，尝试仪表板
        try:
            dashboard_result = self._fetch_dashboard_page()
            if dashboard_result:
                result.update(dashboard_result)
                print("✅ 仪表板数据获取成功")
                return result
        except Exception as e:
            print(f"❌ 仪表板获取失败: {e}")
        
        # 如果都失败，返回基本信息
        print("⚠️ 使用基本信息")
        return result
    
    def _fetch_subscription_page(self) -> Optional[Dict]:
        """获取订阅页面信息"""
        try:
            url = f"{self.base_url}/dashboard/subscription"
            response = self.session.get(url, timeout=self.timeout)
            
            if response.status_code == 200:
                return self._parse_subscription_html(response.text)
        except Exception as e:
            print(f"订阅页面获取失败: {e}")
        
        return None
    
    def _fetch_dashboard_page(self) -> Optional[Dict]:
        """获取仪表板页面信息"""
        try:
            url = f"{self.base_url}/dashboard"
            response = self.session.get(url, timeout=self.timeout)
            
            if response.status_code == 200:
                return self._parse_dashboard_html(response.text)
        except Exception as e:
            print(f"仪表板页面获取失败: {e}")
        
        return None
    
    def _parse_subscription_html(self, html: str) -> Dict:
        """解析订阅页面HTML"""
        result = {}
        
        try:
            # 查找剩余次数
            available_patterns = [
                r'(\d+(?:\.\d+)?)\s+available',
                r'available["\']?\s*:\s*(\d+(?:\.\d+)?)',
                r'remaining["\']?\s*:\s*(\d+(?:\.\d+)?)'
            ]
            
            for pattern in available_patterns:
                match = re.search(pattern, html, re.IGNORECASE)
                if match:
                    remaining_value = float(match.group(1))
                    result['remaining_count'] = int(remaining_value) if remaining_value.is_integer() else remaining_value
                    break
            
            # 查找使用情况
            used_patterns = [
                r'Used\s+(\d+)\s+of\s+(\d+)\s+this\s+month',
                r'(\d+)\s*/\s*(\d+)\s*(?:messages?|requests?|uses?)'
            ]
            
            for pattern in used_patterns:
                match = re.search(pattern, html, re.IGNORECASE)
                if match:
                    result['usage_count'] = int(match.group(1))
                    result['usage_limit'] = int(match.group(2))
                    if 'remaining_count' not in result:
                        result['remaining_count'] = result['usage_limit'] - result['usage_count']
                    break
            
            # 查找计划名称
            plan_patterns = [
                r'(Trial\s+Plan)',
                r'(Pro\s+Plan)',
                r'(Community\s+Plan)',
                r'plan["\']?\s*:\s*["\']([^"\']+)["\']'
            ]
            
            for pattern in plan_patterns:
                match = re.search(pattern, html, re.IGNORECASE)
                if match:
                    result['plan_name'] = match.group(1).strip()
                    break
            
            # 查找日期
            date_patterns = [
                r'(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d+,\s+\d{4}',
                r'(\d{4}-\d{2}-\d{2})'
            ]
            
            for pattern in date_patterns:
                match = re.search(pattern, html, re.IGNORECASE)
                if match:
                    result['reset_date'] = match.group(0).strip()
                    break
            
        except Exception as e:
            result['parse_error'] = str(e)
        
        return result
    
    def _parse_dashboard_html(self, html: str) -> Dict:
        """解析仪表板页面HTML"""
        result = {}
        
        try:
            # 查找用户名
            username_patterns = [
                r'Welcome,?\s+([^<,!]+)',
                r'Hello,?\s+([^<,!]+)',
                r'username["\']?\s*:\s*["\']([^"\']+)["\']'
            ]
            
            for pattern in username_patterns:
                match = re.search(pattern, html, re.IGNORECASE)
                if match:
                    username = match.group(1).strip()
                    if len(username) < 50:
                        result['username'] = username
                        break
            
            # 查找使用统计
            stats_patterns = [
                r'(\d+)\s*(?:messages?|requests?|queries?)\s*(?:used|sent|made)',
                r'usage["\']?\s*:\s*(\d+)'
            ]
            
            for pattern in stats_patterns:
                match = re.search(pattern, html, re.IGNORECASE)
                if match:
                    result['usage_count'] = int(match.group(1))
                    break
            
        except Exception as e:
            result['parse_error'] = str(e)
        
        return result


# 全局实例
_enhanced_checker = None


def get_enhanced_account_checker() -> EnhancedAccountChecker:
    """获取增强账号检查器实例"""
    global _enhanced_checker
    if _enhanced_checker is None:
        _enhanced_checker = EnhancedAccountChecker()
    return _enhanced_checker


def get_enhanced_account_info(cookie_string: str) -> Tuple[bool, Dict]:
    """获取增强的账号信息（便捷函数）"""
    checker = get_enhanced_account_checker()
    return checker.get_enhanced_account_info(cookie_string)
