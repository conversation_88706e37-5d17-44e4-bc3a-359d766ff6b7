#!/usr/bin/env python3
"""
调试登录问题
"""

import os
import sys
import traceback

def debug_login_issue():
    """调试登录问题"""
    print("🔍 调试登录问题")
    print("=" * 50)
    
    # 1. 检查基本导入
    print("\n1️⃣ 检查基本导入:")
    try:
        from utils.paths import get_app_data_dir
        print("✅ paths 模块导入成功")
        
        app_data_dir = get_app_data_dir()
        print(f"✅ 应用数据目录: {app_data_dir}")
        
    except Exception as e:
        print(f"❌ paths 模块导入失败: {e}")
        traceback.print_exc()
        return
    
    # 2. 检查账号管理器导入
    print("\n2️⃣ 检查账号管理器导入:")
    try:
        from utils.account_manager import get_current_login_info, save_login_info, clear_login_info
        print("✅ account_manager 模块导入成功")
        
    except Exception as e:
        print(f"❌ account_manager 模块导入失败: {e}")
        traceback.print_exc()
        return
    
    # 3. 检查配置目录
    print("\n3️⃣ 检查配置目录:")
    config_dir = os.path.join(app_data_dir, "augment_accounts")
    login_file = os.path.join(app_data_dir, "login_info.json")
    
    print(f"配置目录: {config_dir}")
    print(f"登录文件: {login_file}")
    print(f"配置目录存在: {'✅ 是' if os.path.exists(config_dir) else '❌ 否'}")
    print(f"登录文件存在: {'✅ 是' if os.path.exists(login_file) else '❌ 否'}")
    
    # 4. 测试保存登录信息
    print("\n4️⃣ 测试保存登录信息:")
    test_email = "<EMAIL>"
    test_cookie = "session=test123; user=testuser"
    test_username = "测试用户"
    
    try:
        result = save_login_info(test_email, test_cookie, test_username)
        print(f"保存结果: {'✅ 成功' if result else '❌ 失败'}")
        
        if result:
            print(f"登录文件现在存在: {'✅ 是' if os.path.exists(login_file) else '❌ 否'}")
            
    except Exception as e:
        print(f"❌ 保存失败: {e}")
        traceback.print_exc()
    
    # 5. 测试读取登录信息
    print("\n5️⃣ 测试读取登录信息:")
    try:
        login_info = get_current_login_info()
        if login_info:
            print("✅ 读取成功:")
            print(f"   邮箱: {login_info.get('email')}")
            print(f"   用户名: {login_info.get('username')}")
            print(f"   Cookie长度: {len(login_info.get('cookie_string', ''))} 字符")
            print(f"   最后使用: {login_info.get('last_used')}")
        else:
            print("❌ 读取失败或无数据")
            
    except Exception as e:
        print(f"❌ 读取失败: {e}")
        traceback.print_exc()
    
    # 6. 检查账号检测器
    print("\n6️⃣ 检查账号检测器:")
    try:
        from utils.account_detector import get_all_account_info
        print("✅ account_detector 模块导入成功")
        
        account_info = get_all_account_info()
        augment_account = account_info.get('augment_account', {})
        
        print(f"检测结果:")
        print(f"   登录状态: {'✅ 已登录' if augment_account.get('logged_in') else '❌ 未登录'}")
        if augment_account.get('logged_in'):
            print(f"   邮箱: {augment_account.get('email')}")
            print(f"   用户名: {augment_account.get('username')}")
            print(f"   登录方式: {augment_account.get('login_method')}")
        
    except Exception as e:
        print(f"❌ account_detector 检测失败: {e}")
        traceback.print_exc()
    
    # 7. 检查文件内容
    print("\n7️⃣ 检查文件内容:")
    if os.path.exists(login_file):
        try:
            with open(login_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"文件大小: {len(content)} 字符")
                print(f"文件内容预览: {content[:200]}...")
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
    else:
        print("❌ 登录文件不存在")
    
    # 8. 清理测试数据
    print("\n8️⃣ 清理测试数据:")
    try:
        clear_result = clear_login_info()
        print(f"清理结果: {'✅ 成功' if clear_result else '❌ 失败'}")
    except Exception as e:
        print(f"❌ 清理失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 调试完成")

if __name__ == "__main__":
    debug_login_issue()
