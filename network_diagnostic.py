#!/usr/bin/env python3
"""
网络连接诊断工具
用于诊断AugmentCode网站连接问题
"""

import requests
import time
import socket
from datetime import datetime
from typing import Dict, List, Tuple


def test_basic_connectivity() -> bool:
    """测试基本网络连接"""
    print("🌐 测试基本网络连接...")
    
    test_urls = [
        "https://www.google.com",
        "https://www.github.com",
        "https://httpbin.org/get"
    ]
    
    success_count = 0
    for url in test_urls:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"✅ {url} - 连接成功")
                success_count += 1
            else:
                print(f"⚠️ {url} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ {url} - 连接失败: {e}")
    
    success_rate = success_count / len(test_urls)
    print(f"📊 基本连接成功率: {success_rate:.1%}")
    
    return success_rate > 0.5


def test_augmentcode_connectivity() -> Dict:
    """测试AugmentCode网站连接"""
    print("\n🎯 测试AugmentCode网站连接...")
    
    base_url = "https://app.augmentcode.com"
    test_paths = [
        "/",
        "/dashboard",
        "/dashboard/subscription",
        "/login"
    ]
    
    results = {}
    session = requests.Session()
    
    # 设置真实的请求头
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    })
    
    for path in test_paths:
        url = base_url + path
        try:
            start_time = time.time()
            response = session.get(url, timeout=15, allow_redirects=True)
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000  # 转换为毫秒
            
            results[path] = {
                'status_code': response.status_code,
                'response_time': response_time,
                'success': response.status_code in [200, 302, 401],  # 401也算正常，说明服务器响应了
                'content_length': len(response.content),
                'headers': dict(response.headers)
            }
            
            status = "✅" if results[path]['success'] else "❌"
            print(f"{status} {path} - 状态码: {response.status_code}, 响应时间: {response_time:.0f}ms")
            
        except requests.exceptions.ConnectionError as e:
            results[path] = {'error': 'ConnectionError', 'details': str(e)}
            print(f"❌ {path} - 连接错误: {e}")
        except requests.exceptions.Timeout as e:
            results[path] = {'error': 'Timeout', 'details': str(e)}
            print(f"⏰ {path} - 超时: {e}")
        except Exception as e:
            results[path] = {'error': 'Unknown', 'details': str(e)}
            print(f"❌ {path} - 未知错误: {e}")
        
        # 添加延迟避免被限制
        time.sleep(1)
    
    return results


def test_dns_resolution() -> bool:
    """测试DNS解析"""
    print("\n🔍 测试DNS解析...")
    
    hostname = "app.augmentcode.com"
    
    try:
        ip_addresses = socket.gethostbyname_ex(hostname)
        print(f"✅ {hostname} 解析成功")
        print(f"   IP地址: {ip_addresses[2]}")
        return True
    except socket.gaierror as e:
        print(f"❌ {hostname} DNS解析失败: {e}")
        return False
    except Exception as e:
        print(f"❌ DNS解析异常: {e}")
        return False


def test_with_different_headers() -> Dict:
    """测试不同请求头的效果"""
    print("\n🧪 测试不同请求头...")
    
    url = "https://app.augmentcode.com/dashboard"
    
    header_sets = {
        "Chrome": {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        },
        "Firefox": {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
        },
        "Edge": {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0'
        },
        "Minimal": {
            'User-Agent': 'Python-requests/2.31.0'
        }
    }
    
    results = {}
    
    for name, headers in header_sets.items():
        try:
            response = requests.get(url, headers=headers, timeout=10)
            results[name] = {
                'status_code': response.status_code,
                'success': response.status_code in [200, 302, 401]
            }
            
            status = "✅" if results[name]['success'] else "❌"
            print(f"{status} {name} - 状态码: {response.status_code}")
            
        except Exception as e:
            results[name] = {'error': str(e)}
            print(f"❌ {name} - 失败: {e}")
        
        time.sleep(1)
    
    return results


def test_cookie_authentication() -> bool:
    """测试Cookie认证"""
    print("\n🍪 测试Cookie认证...")
    
    try:
        from utils.simple_account_manager import get_current_login_info
        login_info = get_current_login_info()
        
        if not login_info:
            print("❌ 未找到登录信息")
            return False
        
        cookie_string = login_info.get('cookie_string')
        if not cookie_string:
            print("❌ 未找到Cookie信息")
            return False
        
        print(f"📧 测试账号: {login_info.get('email', 'Unknown')}")
        print(f"🍪 Cookie长度: {len(cookie_string)} 字符")
        
        # 解析Cookie
        cookies = {}
        if ';' in cookie_string:
            for item in cookie_string.split(';'):
                if '=' in item:
                    key, value = item.strip().split('=', 1)
                    cookies[key] = value
        
        print(f"🔑 Cookie数量: {len(cookies)}")
        
        # 测试带Cookie的请求
        session = requests.Session()
        for key, value in cookies.items():
            session.cookies.set(key, value)
        
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Referer': 'https://app.augmentcode.com/dashboard'
        })
        
        test_url = "https://app.augmentcode.com/dashboard/subscription"
        response = session.get(test_url, timeout=10)
        
        print(f"🔍 认证测试结果: 状态码 {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Cookie认证成功")
            return True
        elif response.status_code == 401:
            print("❌ Cookie已过期或无效")
            return False
        else:
            print(f"⚠️ 未知状态: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Cookie认证测试失败: {e}")
        return False


def generate_diagnostic_report(results: Dict) -> str:
    """生成诊断报告"""
    report = []
    report.append("=" * 60)
    report.append("🔧 AugmentCode网络连接诊断报告")
    report.append("=" * 60)
    report.append(f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # 基本连接测试
    if results.get('basic_connectivity'):
        report.append("✅ 基本网络连接: 正常")
    else:
        report.append("❌ 基本网络连接: 异常")
    
    # DNS解析测试
    if results.get('dns_resolution'):
        report.append("✅ DNS解析: 正常")
    else:
        report.append("❌ DNS解析: 失败")
    
    # AugmentCode连接测试
    augment_results = results.get('augmentcode_connectivity', {})
    success_count = sum(1 for r in augment_results.values() if r.get('success', False))
    total_count = len(augment_results)
    
    if success_count > 0:
        report.append(f"✅ AugmentCode连接: {success_count}/{total_count} 成功")
    else:
        report.append("❌ AugmentCode连接: 全部失败")
    
    # Cookie认证测试
    if results.get('cookie_auth'):
        report.append("✅ Cookie认证: 有效")
    else:
        report.append("❌ Cookie认证: 无效或失败")
    
    report.append("")
    report.append("💡 建议:")
    
    if not results.get('basic_connectivity'):
        report.append("   - 检查网络连接")
        report.append("   - 检查防火墙设置")
    
    if not results.get('dns_resolution'):
        report.append("   - 尝试更换DNS服务器")
        report.append("   - 检查hosts文件")
    
    if success_count == 0:
        report.append("   - 可能被反爬虫机制阻止")
        report.append("   - 尝试使用VPN或代理")
        report.append("   - 减少请求频率")
    
    if not results.get('cookie_auth'):
        report.append("   - 重新登录获取新的Cookie")
        report.append("   - 检查Cookie格式是否正确")
    
    return "\n".join(report)


def main():
    """主诊断函数"""
    print("🚀 开始AugmentCode网络连接诊断")
    print("=" * 60)
    
    results = {}
    
    # 1. 基本连接测试
    results['basic_connectivity'] = test_basic_connectivity()
    
    # 2. DNS解析测试
    results['dns_resolution'] = test_dns_resolution()
    
    # 3. AugmentCode连接测试
    results['augmentcode_connectivity'] = test_augmentcode_connectivity()
    
    # 4. 不同请求头测试
    results['header_tests'] = test_with_different_headers()
    
    # 5. Cookie认证测试
    results['cookie_auth'] = test_cookie_authentication()
    
    # 生成报告
    report = generate_diagnostic_report(results)
    print("\n" + report)
    
    # 保存报告到文件
    try:
        with open("network_diagnostic_report.txt", "w", encoding="utf-8") as f:
            f.write(report)
        print(f"\n📄 诊断报告已保存到: network_diagnostic_report.txt")
    except Exception as e:
        print(f"⚠️ 保存报告失败: {e}")


if __name__ == "__main__":
    main()
