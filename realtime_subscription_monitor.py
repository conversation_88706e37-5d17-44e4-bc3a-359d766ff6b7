#!/usr/bin/env python3
"""
实时订阅监控器
实时获取AugmentCode最新的使用情况
"""

import requests
import json
import re
import time
import os
from datetime import datetime
from typing import Dict, Optional


class RealtimeSubscriptionMonitor:
    """实时订阅监控器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.cookie_string = ""
        self.last_data = {}
        self.monitoring = False
        
    def setup_session(self):
        """设置会话"""
        try:
            # 读取登录信息
            with open("data/login_info.json", "r", encoding="utf-8") as f:
                login_info = json.load(f)
            
            self.cookie_string = login_info.get('cookie_string', '')
            
            # 解析Cookie
            cookies = {}
            if self.cookie_string.startswith('_session='):
                # 直接是_session格式
                key, value = self.cookie_string.split('=', 1)
                cookies[key] = value
            elif ';' in self.cookie_string:
                # 多个Cookie格式
                for item in self.cookie_string.split(';'):
                    if '=' in item:
                        key, value = item.strip().split('=', 1)
                        cookies[key] = value
            
            # 设置Cookie
            for key, value in cookies.items():
                self.session.cookies.set(key, value)
            
            # 设置请求头
            self.session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Referer': 'https://app.augmentcode.com/',
                'Upgrade-Insecure-Requests': '1'
            })
            
            print("✅ 会话设置完成")
            return True
            
        except Exception as e:
            print(f"❌ 会话设置失败: {e}")
            return False
    
    def fetch_subscription_data(self) -> Optional[Dict]:
        """获取订阅数据"""
        try:
            # 尝试访问订阅页面
            url = "https://app.augmentcode.com/subscription"
            
            print(f"🔄 请求订阅页面: {url}")
            response = self.session.get(url, timeout=15)
            
            if response.status_code == 200:
                return self.parse_subscription_page(response.text)
            elif response.status_code == 404:
                # 尝试其他可能的URL
                alternative_urls = [
                    "https://app.augmentcode.com/dashboard/subscription",
                    "https://app.augmentcode.com/billing",
                    "https://app.augmentcode.com/account/subscription"
                ]
                
                for alt_url in alternative_urls:
                    print(f"🔄 尝试备用URL: {alt_url}")
                    response = self.session.get(alt_url, timeout=15)
                    if response.status_code == 200:
                        return self.parse_subscription_page(response.text)
            
            print(f"⚠️ 订阅页面访问失败: {response.status_code}")
            return None
            
        except Exception as e:
            print(f"❌ 获取订阅数据失败: {e}")
            return None
    
    def parse_subscription_page(self, html_content: str) -> Dict:
        """解析订阅页面"""
        try:
            data = {
                'timestamp': datetime.now().isoformat(),
                'source': 'subscription_page'
            }
            
            # 解析可用消息数
            available_patterns = [
                r'(\d+\.?\d*)\s+available',
                r'available["\']?\s*:\s*(\d+\.?\d*)',
                r'remaining["\']?\s*:\s*(\d+\.?\d*)'
            ]
            
            for pattern in available_patterns:
                match = re.search(pattern, html_content, re.IGNORECASE)
                if match:
                    data['available'] = float(match.group(1))
                    break
            
            # 解析已使用数量
            used_patterns = [
                r'Used\s+(\d+)\s+of\s+(\d+)\s+this\s+month',
                r'used["\']?\s*:\s*(\d+)',
                r'(\d+)\s+used'
            ]
            
            for pattern in used_patterns:
                match = re.search(pattern, html_content, re.IGNORECASE)
                if match:
                    if 'of' in pattern:
                        data['used'] = int(match.group(1))
                        data['limit'] = int(match.group(2))
                    else:
                        data['used'] = int(match.group(1))
                    break
            
            # 解析计划信息
            plan_patterns = [
                r'(Community\s+Plan)',
                r'(Trial\s+Plan)',
                r'(Pro\s+Plan)',
                r'plan["\']?\s*:\s*["\']([^"\']+)["\']'
            ]
            
            for pattern in plan_patterns:
                match = re.search(pattern, html_content, re.IGNORECASE)
                if match:
                    data['plan'] = match.group(1)
                    break
            
            # 解析下次计费日期
            date_patterns = [
                r'(\w+\s+\d+,\s+\d+)',
                r'(\d{4}-\d{2}-\d{2})',
                r'next.*?billing.*?(\w+\s+\d+,\s+\d+)'
            ]
            
            for pattern in date_patterns:
                match = re.search(pattern, html_content, re.IGNORECASE)
                if match:
                    data['next_billing'] = match.group(1)
                    break
            
            # 解析月费
            price_patterns = [
                r'\$(\d+\.?\d*)/user/mo',
                r'\$(\d+\.?\d*)\s*monthly',
                r'Monthly\s+total:\s*\$(\d+\.?\d*)'
            ]
            
            for pattern in price_patterns:
                match = re.search(pattern, html_content, re.IGNORECASE)
                if match:
                    data['monthly_price'] = f"${match.group(1)}"
                    break
            
            print(f"✅ 解析成功: {data}")
            return data
            
        except Exception as e:
            print(f"❌ 页面解析失败: {e}")
            return {}
    
    def update_local_data(self, new_data: Dict):
        """更新本地数据"""
        try:
            if not new_data:
                return False
            
            # 更新账号缓存
            cache_data = {
                'email': '<EMAIL>',
                'username': 'opggozyduc',
                'plan_name': new_data.get('plan', 'Community Plan'),
                'usage_count': new_data.get('used', 0),
                'usage_limit': new_data.get('limit', 50),
                'remaining_count': new_data.get('available', 0),
                'reset_date': self.format_date(new_data.get('next_billing', 'July 11, 2025')),
                'subscription_status': 'active',
                'monthly_total': new_data.get('monthly_price', '$0.00'),
                'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'data_source': 'realtime_monitor'
            }
            
            # 保存到缓存文件
            os.makedirs("data", exist_ok=True)
            with open("data/account_cache.json", "w", encoding="utf-8") as f:
                json.dump(cache_data, f, indent=2, ensure_ascii=False)
            
            # 更新UI覆盖文件
            ui_override = {
                "force_display": True,
                "display_data": {
                    "email": cache_data['email'],
                    "plan_name": cache_data['plan_name'],
                    "usage_display": f"已使用{cache_data['usage_count']}次",
                    "remaining_display": f"{cache_data['remaining_count']:.1f} available",
                    "reset_date": cache_data['reset_date'],
                    "billing_info": cache_data['monthly_total']
                },
                "timestamp": cache_data['last_updated']
            }
            
            with open("data/ui_override.json", "w", encoding="utf-8") as f:
                json.dump(ui_override, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 本地数据已更新")
            print(f"   📊 可用: {cache_data['remaining_count']}")
            print(f"   📊 已用: {cache_data['usage_count']}/{cache_data['usage_limit']}")
            
            return True
            
        except Exception as e:
            print(f"❌ 本地数据更新失败: {e}")
            return False
    
    def format_date(self, date_str: str) -> str:
        """格式化日期"""
        try:
            if 'July 11, 2025' in date_str:
                return '2025年7月11日'
            # 可以添加更多日期格式转换
            return date_str
        except:
            return date_str
    
    def monitor_once(self) -> bool:
        """执行一次监控"""
        print(f"\n🔄 开始监控 - {datetime.now().strftime('%H:%M:%S')}")
        
        if not self.setup_session():
            return False
        
        new_data = self.fetch_subscription_data()
        if new_data:
            # 检查是否有变化
            if new_data != self.last_data:
                print("📊 检测到数据变化!")
                self.update_local_data(new_data)
                self.last_data = new_data
                
                # 触发UI刷新
                try:
                    from utils.realtime_account_refresher import force_refresh_account_info
                    force_refresh_account_info()
                    print("✅ UI刷新已触发")
                except:
                    print("⚠️ UI刷新触发失败")
                
                return True
            else:
                print("📊 数据无变化")
                return True
        else:
            print("❌ 数据获取失败")
            return False
    
    def start_monitoring(self, interval: int = 30):
        """开始持续监控"""
        print(f"🚀 开始实时监控 (间隔: {interval}秒)")
        self.monitoring = True
        
        try:
            while self.monitoring:
                self.monitor_once()
                
                if self.monitoring:  # 检查是否仍在监控
                    print(f"⏰ 等待 {interval} 秒...")
                    time.sleep(interval)
                    
        except KeyboardInterrupt:
            print("\n⏹️ 监控已停止")
        except Exception as e:
            print(f"❌ 监控异常: {e}")
        finally:
            self.monitoring = False
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        print("⏹️ 停止监控请求已发送")


def main():
    """主函数"""
    print("🔄 AugmentCode实时订阅监控器")
    print("=" * 60)
    
    monitor = RealtimeSubscriptionMonitor()
    
    try:
        # 先执行一次立即监控
        print("🎯 执行立即监控...")
        if monitor.monitor_once():
            print("\n✅ 立即监控完成")
            
            # 询问是否开始持续监控
            print("\n💡 选项:")
            print("1. 仅执行一次监控 (已完成)")
            print("2. 开始持续监控 (每30秒)")
            print("3. 开始快速监控 (每10秒)")
            
            try:
                choice = input("\n请选择 (1/2/3): ").strip()
                
                if choice == "2":
                    monitor.start_monitoring(30)
                elif choice == "3":
                    monitor.start_monitoring(10)
                else:
                    print("✅ 单次监控完成")
                    
            except KeyboardInterrupt:
                print("\n⏹️ 用户取消")
        else:
            print("\n❌ 立即监控失败")
            
    except Exception as e:
        print(f"❌ 监控器异常: {e}")
    
    print("\n" + "=" * 60)
    print("💡 监控完成！请检查主程序界面的更新")


if __name__ == "__main__":
    main()
