#!/usr/bin/env python3
"""
Cookie认证修复工具
"""

import json
import re
from datetime import datetime
from typing import Dict, Optional, <PERSON><PERSON>


def analyze_current_cookie():
    """分析当前Cookie"""
    print("🔍 分析当前Cookie...")
    
    try:
        from utils.simple_account_manager import get_current_login_info
        login_info = get_current_login_info()
        
        if not login_info:
            print("❌ 未找到登录信息")
            return None
        
        cookie_string = login_info.get('cookie_string', '')
        email = login_info.get('email', '')
        
        print(f"📧 账号: {email}")
        print(f"🍪 原始Cookie: {cookie_string[:100]}...")
        print(f"📏 Cookie长度: {len(cookie_string)} 字符")
        
        # 分析Cookie格式
        if not cookie_string:
            print("❌ Cookie为空")
            return None
        
        # 检查Cookie格式
        if ';' in cookie_string:
            print("✅ 检测到标准Cookie格式（分号分隔）")
            parts = cookie_string.split(';')
            print(f"🔢 Cookie部分数量: {len(parts)}")
            
            valid_parts = 0
            for i, part in enumerate(parts):
                part = part.strip()
                if '=' in part:
                    key, value = part.split('=', 1)
                    print(f"   {i+1}. {key.strip()} = {value.strip()[:20]}...")
                    valid_parts += 1
                else:
                    print(f"   {i+1}. 无效部分: {part}")
            
            print(f"✅ 有效Cookie部分: {valid_parts}/{len(parts)}")
            
        elif '=' in cookie_string:
            print("⚠️ 检测到单个Cookie格式")
            key, value = cookie_string.split('=', 1)
            print(f"   键: {key}")
            print(f"   值: {value[:50]}...")
        else:
            print("❌ 无法识别的Cookie格式")
            print("💡 可能是纯token格式，需要转换")
        
        return cookie_string
        
    except Exception as e:
        print(f"❌ Cookie分析失败: {e}")
        return None


def validate_cookie_format(cookie_string: str) -> Tuple[bool, Dict]:
    """验证Cookie格式"""
    print("\n🔧 验证Cookie格式...")
    
    result = {
        'valid': False,
        'format_type': 'unknown',
        'cookies': {},
        'issues': []
    }
    
    if not cookie_string:
        result['issues'].append('Cookie为空')
        return False, result
    
    try:
        # 尝试解析标准格式
        if ';' in cookie_string:
            result['format_type'] = 'standard'
            cookies = {}
            
            for part in cookie_string.split(';'):
                part = part.strip()
                if '=' in part:
                    key, value = part.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    
                    if key and value:
                        cookies[key] = value
                    else:
                        result['issues'].append(f'空的键或值: {part}')
                else:
                    result['issues'].append(f'无效格式: {part}')
            
            result['cookies'] = cookies
            result['valid'] = len(cookies) > 0
            
        elif '=' in cookie_string:
            result['format_type'] = 'single'
            key, value = cookie_string.split('=', 1)
            key = key.strip()
            value = value.strip()
            
            if key and value:
                result['cookies'][key] = value
                result['valid'] = True
            else:
                result['issues'].append('单个Cookie的键或值为空')
        
        else:
            result['format_type'] = 'token'
            result['issues'].append('可能是纯token格式，需要确定正确的键名')
        
        print(f"📊 格式类型: {result['format_type']}")
        print(f"✅ 有效性: {'是' if result['valid'] else '否'}")
        print(f"🔑 Cookie数量: {len(result['cookies'])}")
        
        if result['issues']:
            print("⚠️ 发现的问题:")
            for issue in result['issues']:
                print(f"   - {issue}")
        
        return result['valid'], result
        
    except Exception as e:
        result['issues'].append(f'解析异常: {str(e)}')
        return False, result


def suggest_cookie_fix(cookie_string: str) -> Optional[str]:
    """建议Cookie修复方案"""
    print("\n💡 Cookie修复建议...")
    
    if not cookie_string:
        print("❌ Cookie为空，需要重新获取")
        return None
    
    # 清理常见问题
    fixed_cookie = cookie_string.strip()
    
    # 移除可能的引号
    if fixed_cookie.startswith('"') and fixed_cookie.endswith('"'):
        fixed_cookie = fixed_cookie[1:-1]
        print("🔧 移除了外层引号")
    
    # 移除可能的换行符
    if '\n' in fixed_cookie or '\r' in fixed_cookie:
        fixed_cookie = fixed_cookie.replace('\n', '').replace('\r', '')
        print("🔧 移除了换行符")
    
    # 检查是否需要添加键名
    if '=' not in fixed_cookie and len(fixed_cookie) > 20:
        print("🔧 检测到可能的纯token格式")
        
        # 常见的AugmentCode Cookie键名
        possible_keys = [
            'auth_token',
            'access_token',
            'session_token',
            'jwt_token',
            'augment_session',
            'user_session'
        ]
        
        print("💡 建议尝试以下键名:")
        for key in possible_keys:
            suggested = f"{key}={fixed_cookie}"
            print(f"   - {key}={fixed_cookie[:30]}...")
        
        # 默认使用第一个
        fixed_cookie = f"auth_token={fixed_cookie}"
        print(f"🔧 默认使用: auth_token")
    
    # 验证修复后的Cookie
    valid, result = validate_cookie_format(fixed_cookie)
    
    if valid:
        print("✅ 修复后的Cookie格式有效")
        return fixed_cookie
    else:
        print("❌ 修复后的Cookie仍然无效")
        return None


def test_cookie_authentication(cookie_string: str) -> bool:
    """测试Cookie认证"""
    print(f"\n🧪 测试Cookie认证...")
    
    try:
        import requests
        
        # 解析Cookie
        cookies = {}
        if ';' in cookie_string:
            for part in cookie_string.split(';'):
                if '=' in part:
                    key, value = part.strip().split('=', 1)
                    cookies[key.strip()] = value.strip()
        elif '=' in cookie_string:
            key, value = cookie_string.split('=', 1)
            cookies[key.strip()] = value.strip()
        
        if not cookies:
            print("❌ 无法解析Cookie")
            return False
        
        print(f"🔑 解析到 {len(cookies)} 个Cookie")
        
        # 创建会话
        session = requests.Session()
        for key, value in cookies.items():
            session.cookies.set(key, value)
        
        # 设置请求头
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Referer': 'https://app.augmentcode.com/'
        })
        
        # 测试认证
        test_urls = [
            'https://app.augmentcode.com/dashboard',
            'https://app.augmentcode.com/api/user',
            'https://app.augmentcode.com/profile'
        ]
        
        for url in test_urls:
            try:
                print(f"🔄 测试: {url}")
                response = session.get(url, timeout=10)
                
                if response.status_code == 200:
                    # 检查是否是登录页面
                    if 'Redirecting to login' in response.text or 'Augment Login' in response.text:
                        print(f"❌ 返回登录页面，认证失败")
                    else:
                        print(f"✅ 认证成功: {response.status_code}")
                        return True
                elif response.status_code == 401:
                    print(f"❌ 认证失败: {response.status_code}")
                else:
                    print(f"⚠️ 状态码: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 请求失败: {e}")
        
        return False
        
    except Exception as e:
        print(f"❌ 认证测试失败: {e}")
        return False


def interactive_cookie_fix():
    """交互式Cookie修复"""
    print("\n🔧 交互式Cookie修复向导")
    print("=" * 50)
    
    # 分析当前Cookie
    current_cookie = analyze_current_cookie()
    
    if current_cookie:
        # 验证当前Cookie
        valid, result = validate_cookie_format(current_cookie)
        
        if not valid:
            print("\n❌ 当前Cookie无效，需要修复")
            
            # 尝试自动修复
            fixed_cookie = suggest_cookie_fix(current_cookie)
            
            if fixed_cookie:
                print(f"\n🔧 建议的修复Cookie:")
                print(f"   {fixed_cookie[:100]}...")
                
                # 测试修复后的Cookie
                if test_cookie_authentication(fixed_cookie):
                    print("\n✅ 修复后的Cookie认证成功！")
                    
                    # 询问是否保存
                    try:
                        save = input("\n❓ 是否保存修复后的Cookie？(y/n): ").strip().lower()
                        if save in ['y', 'yes', '是']:
                            # 保存修复后的Cookie
                            from utils.simple_account_manager import get_current_login_info
                            login_info = get_current_login_info()
                            
                            if login_info:
                                login_info['cookie_string'] = fixed_cookie
                                
                                from utils.account_manager import save_login_info
                                if save_login_info(
                                    login_info['email'],
                                    fixed_cookie,
                                    login_info.get('username', '')
                                ):
                                    print("✅ Cookie已保存")
                                else:
                                    print("❌ Cookie保存失败")
                    except KeyboardInterrupt:
                        print("\n⏹️ 用户取消")
                else:
                    print("\n❌ 修复后的Cookie仍然无效")
            else:
                print("\n❌ 无法自动修复Cookie")
        else:
            print("\n✅ 当前Cookie格式有效")
            
            # 测试认证
            if test_cookie_authentication(current_cookie):
                print("✅ Cookie认证成功")
            else:
                print("❌ Cookie认证失败，可能已过期")
    
    print("\n💡 如果问题仍然存在，建议:")
    print("1. 重新登录AugmentCode网站获取新的Cookie")
    print("2. 确保Cookie包含完整的认证信息")
    print("3. 检查Cookie是否已过期")
    print("4. 尝试使用浏览器开发者工具获取完整Cookie")


def main():
    """主函数"""
    print("🔧 AugmentCode Cookie认证修复工具")
    print("=" * 60)
    print(f"📅 运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    interactive_cookie_fix()


if __name__ == "__main__":
    main()
