#!/usr/bin/env python3
"""
账号管理视图
显示和管理AugmentCode账号相关信息
"""

import threading
import tkinter.messagebox as messagebox

import customtkinter as ctk

from utils.account_detector import get_all_account_info
from utils.simple_account_manager import save_login_info, get_current_login_info, clear_login_info


class AccountView:
    """账号管理视图类"""

    def __init__(self, parent_frame):
        """初始化账号管理视图

        Args:
            parent_frame: 父容器框架
        """
        self.parent_frame = parent_frame

        # 创建主框架
        self.main_frame = ctk.CTkFrame(parent_frame, fg_color="transparent")
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 页面标题
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="账号管理",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(0, 20))

        # 加载中标签
        self.loading_label = ctk.CTkLabel(
            self.main_frame,
            text="正在加载账号信息...",
            font=ctk.CTkFont(size=16)
        )
        self.loading_label.pack(pady=50)

        # 内容区域
        self.content_frame = ctk.CTkFrame(self.main_frame, fg_color="transparent")

        # 加载数据
        threading.Thread(target=self._load_data, daemon=True).start()

    def _load_data(self):
        """加载数据"""
        try:
            # 获取所有账号数据
            account_info = get_all_account_info()

            # 在主线程中更新UI
            self.parent_frame.after(0, lambda: self._update_ui(account_info))

        except Exception as e:
            # 显示错误信息
            self.parent_frame.after(0, lambda: self._show_error(str(e)))

    def _update_ui(self, data):
        """更新UI

        Args:
            data: 加载的数据
        """
        # 隐藏加载标签
        self.loading_label.pack_forget()

        # 清除现有内容
        for widget in self.content_frame.winfo_children():
            widget.destroy()

        # 显示内容区域
        self.content_frame.pack(fill="both", expand=True)

        # 获取账号信息
        account_data = data.get('augment_account', {})

        # 首先检查是否有手动输入的登录信息
        manual_login_info = get_current_login_info()

        # 根据登录状态显示不同内容
        # 优先检查手动输入的登录信息，然后检查自动检测的信息
        if manual_login_info or account_data.get('logged_in', False):
            self._create_logged_in_view(account_data)
        else:
            self._create_not_logged_in_view()

    def _create_logged_in_view(self, account_data):
        """创建已登录视图

        Args:
            account_data: 账号数据
        """
        # 账号信息区域
        info_frame = ctk.CTkFrame(self.content_frame)
        info_frame.pack(fill="x", pady=10)

        # 区域标题
        title_frame = ctk.CTkFrame(info_frame, fg_color="#4CAF50", height=36)
        title_frame.pack(fill="x")

        title_label = ctk.CTkLabel(
            title_frame,
            text="✅ 当前账号信息",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color="white"
        )
        title_label.pack(pady=5)

        # 账号详情
        details_frame = ctk.CTkFrame(info_frame, fg_color="transparent")
        details_frame.pack(fill="x", padx=15, pady=15)

        # 账号状态
        status_frame = ctk.CTkFrame(details_frame, fg_color=("gray95", "gray10"))
        status_frame.pack(fill="x", padx=5, pady=5)

        status_label = ctk.CTkLabel(
            status_frame,
            text="✅ 已登录状态",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="#4CAF50"
        )
        status_label.pack(pady=10)

        # 账号信息表格
        info_table = ctk.CTkFrame(details_frame)
        info_table.pack(fill="x", pady=10)

        # 表头
        header_frame = ctk.CTkFrame(info_table, fg_color=("gray85", "gray20"), height=30)
        header_frame.pack(fill="x")

        header_label1 = ctk.CTkLabel(
            header_frame,
            text="信息项",
            font=ctk.CTkFont(size=12, weight="bold"),
            width=100
        )
        header_label1.pack(side="left", padx=10, pady=5)

        header_label2 = ctk.CTkLabel(
            header_frame,
            text="值",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        header_label2.pack(side="left", padx=10, pady=5)

        # 检查是否有手动输入的登录信息
        manual_login_info = get_current_login_info()

        # 表格内容
        if manual_login_info:
            # 显示手动输入的信息
            items = [
                ("账号邮箱", manual_login_info.get('email', '未知邮箱')),
                ("用户名", manual_login_info.get('username', '未知用户')),
                ("登录时间", manual_login_info.get('last_used', '未知时间')),
                ("登录方式", "手动输入"),
                ("Cookie状态", "已保存" if manual_login_info.get('cookie_string') else "未保存")
            ]
        else:
            # 显示检测到的信息
            items = [
                ("账号邮箱", account_data.get('email', '未知邮箱')),
                ("用户名", account_data.get('username', '未知用户')),
                ("登录时间", account_data.get('login_time', '未知时间')),
                ("登录方式", account_data.get('login_method', '自动检测')),
                ("配置文件", account_data.get('config_path', '未知'))
            ]

        for i, (item, value) in enumerate(items):
            row_frame = ctk.CTkFrame(info_table, fg_color=("gray90", "gray15") if i % 2 == 0 else "transparent")
            row_frame.pack(fill="x")

            item_label = ctk.CTkLabel(
                row_frame,
                text=item,
                font=ctk.CTkFont(size=12),
                width=100
            )
            item_label.pack(side="left", padx=10, pady=8, anchor="w")

            value_label = ctk.CTkLabel(
                row_frame,
                text=value,
                font=ctk.CTkFont(size=12)
            )
            value_label.pack(side="left", padx=10, pady=8, fill="x")

        # 操作按钮区域
        button_frame = ctk.CTkFrame(self.content_frame, fg_color="transparent")
        button_frame.pack(fill="x", pady=20)

        refresh_button = ctk.CTkButton(
            button_frame,
            text="🔄 刷新状态",
            command=lambda: threading.Thread(target=self._load_data, daemon=True).start(),
            height=32,
            width=120
        )
        refresh_button.pack(side="left", padx=5)

        # 添加手动输入按钮（始终显示）
        manual_input_button = ctk.CTkButton(
            button_frame,
            text="✏️ 手动输入账号",
            command=self._show_edit_login_dialog,
            height=32,
            width=140,
            fg_color="#4CAF50",
            hover_color="#45a049"
        )
        manual_input_button.pack(side="left", padx=5)

        # 如果有手动输入的登录信息，显示编辑和清除按钮
        manual_login_info = get_current_login_info()
        if manual_login_info:
            edit_button = ctk.CTkButton(
                button_frame,
                text="🔧 编辑当前账号",
                command=self._show_edit_login_dialog,
                height=32,
                width=140,
                fg_color="orange",
                hover_color="darkorange"
            )
            edit_button.pack(side="left", padx=5)

            clear_button = ctk.CTkButton(
                button_frame,
                text="🗑️ 清除登录信息",
                command=self._clear_manual_login,
                height=32,
                width=140,
                fg_color="red",
                hover_color="darkred"
            )
            clear_button.pack(side="left", padx=5)

    def _create_not_logged_in_view(self):
        """创建未登录视图"""
        # 创建滚动框架
        scroll_frame = ctk.CTkScrollableFrame(self.content_frame)
        scroll_frame.pack(fill="both", expand=True)

        # 状态显示区域
        status_frame = ctk.CTkFrame(scroll_frame)
        status_frame.pack(fill="x", padx=10, pady=10)

        icon_label = ctk.CTkLabel(
            status_frame,
            text="👤",
            font=ctk.CTkFont(size=48)
        )
        icon_label.pack(pady=(20, 10))

        title_label = ctk.CTkLabel(
            status_frame,
            text="未检测到登录账号",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=10)

        description_label = ctk.CTkLabel(
            status_frame,
            text="请手动输入您的AugmentCode账号信息",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color="#4CAF50"
        )
        description_label.pack(pady=10)

        hint_label = ctk.CTkLabel(
            status_frame,
            text="输入您的邮箱和从浏览器获取的Cookie即可登录",
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        hint_label.pack(pady=(0, 20))

        # 手动登录区域
        login_frame = ctk.CTkFrame(scroll_frame)
        login_frame.pack(fill="x", padx=10, pady=10)

        login_title = ctk.CTkLabel(
            login_frame,
            text="手动输入登录信息",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        login_title.pack(pady=15)

        # 邮箱输入
        email_label = ctk.CTkLabel(login_frame, text="邮箱地址:")
        email_label.pack(anchor="w", padx=20, pady=(10, 5))

        self.email_entry = ctk.CTkEntry(
            login_frame,
            placeholder_text="请输入您的AugmentCode账号邮箱",
            width=400
        )
        self.email_entry.pack(padx=20, pady=(0, 10))

        # 用户名输入
        username_label = ctk.CTkLabel(login_frame, text="用户名:")
        username_label.pack(anchor="w", padx=20, pady=(10, 5))

        self.username_entry = ctk.CTkEntry(
            login_frame,
            placeholder_text="请输入您的用户名（可选）",
            width=400
        )
        self.username_entry.pack(padx=20, pady=(0, 10))

        # Cookie输入
        cookie_label = ctk.CTkLabel(login_frame, text="Cookie字符串:")
        cookie_label.pack(anchor="w", padx=20, pady=(10, 5))

        self.cookie_text = ctk.CTkTextbox(
            login_frame,
            height=100,
            width=400
        )
        self.cookie_text.pack(padx=20, pady=(0, 10))

        # Cookie说明和帮助
        cookie_help_frame = ctk.CTkFrame(login_frame, fg_color="transparent")
        cookie_help_frame.pack(fill="x", padx=20, pady=5)

        cookie_help = ctk.CTkLabel(
            cookie_help_frame,
            text="提示：从浏览器中复制AugmentCode网站的Cookie信息",
            font=ctk.CTkFont(size=12),
            text_color="gray",
            justify="left"
        )
        cookie_help.pack(side="left")

        help_button = ctk.CTkButton(
            cookie_help_frame,
            text="❓ 获取帮助",
            command=self._show_cookie_help,
            width=100,
            height=25,
            font=ctk.CTkFont(size=11),
            fg_color="blue",
            hover_color="darkblue"
        )
        help_button.pack(side="right")

        # 操作按钮
        button_frame = ctk.CTkFrame(login_frame, fg_color="transparent")
        button_frame.pack(fill="x", padx=20, pady=20)

        save_button = ctk.CTkButton(
            button_frame,
            text="💾 保存登录信息",
            command=self._save_manual_login,
            width=150,
            height=40,
            fg_color="#4CAF50",
            hover_color="#45a049"
        )
        save_button.pack(side="left", padx=10)

        refresh_button = ctk.CTkButton(
            button_frame,
            text="🔄 刷新状态",
            command=lambda: threading.Thread(target=self._load_data, daemon=True).start(),
            width=120,
            height=40,
            fg_color="gray",
            hover_color="darkgray"
        )
        refresh_button.pack(side="left", padx=10)

        # 添加快速输入按钮
        quick_input_button = ctk.CTkButton(
            button_frame,
            text="⚡ 快速输入对话框",
            command=self._show_quick_login_dialog,
            width=150,
            height=40,
            fg_color="blue",
            hover_color="darkblue"
        )
        quick_input_button.pack(side="left", padx=10)

        # 加载现有信息
        self._load_existing_manual_info()

    def _show_error(self, error_message):
        """显示错误信息

        Args:
            error_message: 错误信息
        """
        # 隐藏加载标签
        self.loading_label.pack_forget()

        # 显示错误信息
        error_frame = ctk.CTkFrame(self.main_frame)
        error_frame.pack(fill="both", expand=True, pady=50)

        icon_label = ctk.CTkLabel(
            error_frame,
            text="❌",
            font=ctk.CTkFont(size=48),
            text_color="#F44336"
        )
        icon_label.pack(pady=(30, 10))

        title_label = ctk.CTkLabel(
            error_frame,
            text="加载失败",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color="#F44336"
        )
        title_label.pack(pady=10)

        description_label = ctk.CTkLabel(
            error_frame,
            text=f"错误信息: {error_message}",
            font=ctk.CTkFont(size=14),
            wraplength=500
        )
        description_label.pack(pady=10)

        refresh_button = ctk.CTkButton(
            error_frame,
            text="🔄 重试",
            command=lambda: threading.Thread(target=self._load_data, daemon=True).start(),
            height=32,
            width=120
        )
        refresh_button.pack(pady=10)

    def _load_existing_manual_info(self):
        """加载现有的手动登录信息"""
        try:
            login_info = get_current_login_info()
            if login_info and hasattr(self, 'email_entry'):
                self.email_entry.insert(0, login_info.get('email', ''))
                self.username_entry.insert(0, login_info.get('username', ''))
                self.cookie_text.insert("1.0", login_info.get('cookie_string', ''))
        except Exception as e:
            print(f"加载现有登录信息失败: {e}")

    def _save_manual_login(self):
        """保存手动输入的登录信息"""
        try:
            email = self.email_entry.get().strip()
            username = self.username_entry.get().strip()
            cookie_string = self.cookie_text.get("1.0", "end-1c").strip()

            if not email:
                messagebox.showerror("错误", "请输入邮箱地址")
                return

            if not cookie_string:
                messagebox.showerror("错误", "请输入Cookie字符串")
                return

            # 保存登录信息
            success = save_login_info(email, cookie_string, username)

            if success:
                messagebox.showinfo("成功", "登录信息已保存，正在刷新状态...")
                # 刷新界面
                threading.Thread(target=self._load_data, daemon=True).start()
            else:
                messagebox.showerror("错误", "保存登录信息失败")

        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {str(e)}")

    def _clear_manual_login(self):
        """清除手动输入的登录信息"""
        try:
            result = messagebox.askyesno("确认", "确定要清除手动输入的登录信息吗？")
            if result:
                success = clear_login_info()
                if success:
                    messagebox.showinfo("成功", "登录信息已清除，正在刷新状态...")
                    # 刷新界面
                    threading.Thread(target=self._load_data, daemon=True).start()
                else:
                    messagebox.showerror("错误", "清除登录信息失败")
        except Exception as e:
            messagebox.showerror("错误", f"清除失败: {str(e)}")

    def _show_edit_login_dialog(self):
        """显示编辑登录信息的对话框"""
        try:
            # 创建新窗口
            edit_window = ctk.CTkToplevel(self.parent_frame)
            edit_window.title("编辑登录信息")
            edit_window.geometry("500x600")
            edit_window.transient(self.parent_frame.winfo_toplevel())
            edit_window.grab_set()

            # 窗口居中
            edit_window.update_idletasks()
            x = (edit_window.winfo_screenwidth() // 2) - (500 // 2)
            y = (edit_window.winfo_screenheight() // 2) - (600 // 2)
            edit_window.geometry(f"500x600+{x}+{y}")

            # 标题
            title_label = ctk.CTkLabel(
                edit_window,
                text="编辑登录信息",
                font=ctk.CTkFont(size=20, weight="bold")
            )
            title_label.pack(pady=20)

            # 获取当前登录信息
            current_info = get_current_login_info()

            # 邮箱输入
            email_label = ctk.CTkLabel(edit_window, text="邮箱地址:")
            email_label.pack(anchor="w", padx=20, pady=(10, 5))

            email_entry = ctk.CTkEntry(
                edit_window,
                placeholder_text="请输入您的AugmentCode账号邮箱",
                width=400
            )
            email_entry.pack(padx=20, pady=(0, 10))
            if current_info:
                email_entry.insert(0, current_info.get('email', ''))

            # 用户名输入
            username_label = ctk.CTkLabel(edit_window, text="用户名:")
            username_label.pack(anchor="w", padx=20, pady=(10, 5))

            username_entry = ctk.CTkEntry(
                edit_window,
                placeholder_text="请输入您的用户名（可选）",
                width=400
            )
            username_entry.pack(padx=20, pady=(0, 10))
            if current_info:
                username_entry.insert(0, current_info.get('username', ''))

            # Cookie输入
            cookie_label = ctk.CTkLabel(edit_window, text="Cookie字符串:")
            cookie_label.pack(anchor="w", padx=20, pady=(10, 5))

            cookie_text = ctk.CTkTextbox(
                edit_window,
                height=150,
                width=400
            )
            cookie_text.pack(padx=20, pady=(0, 10))
            if current_info:
                cookie_text.insert("1.0", current_info.get('cookie_string', ''))

            # Cookie说明
            cookie_help = ctk.CTkLabel(
                edit_window,
                text="提示：从浏览器中复制AugmentCode网站的Cookie信息\n登录 https://augmentcode.com 后，按F12打开开发者工具，\n在Network标签页找到请求头中的Cookie值",
                font=ctk.CTkFont(size=12),
                text_color="gray",
                justify="left"
            )
            cookie_help.pack(pady=10)

            # 按钮区域
            button_frame = ctk.CTkFrame(edit_window, fg_color="transparent")
            button_frame.pack(fill="x", padx=20, pady=20)

            def save_and_close():
                """保存并关闭对话框"""
                try:
                    email = email_entry.get().strip()
                    username = username_entry.get().strip()
                    cookie_string = cookie_text.get("1.0", "end-1c").strip()

                    if not email:
                        messagebox.showerror("错误", "请输入邮箱地址")
                        return

                    if not cookie_string:
                        messagebox.showerror("错误", "请输入Cookie字符串")
                        return

                    # 保存登录信息
                    success = save_login_info(email, cookie_string, username)

                    if success:
                        messagebox.showinfo("成功", "登录信息已更新")
                        edit_window.destroy()
                        # 刷新主界面
                        threading.Thread(target=self._load_data, daemon=True).start()
                    else:
                        messagebox.showerror("错误", "保存登录信息失败")

                except Exception as e:
                    messagebox.showerror("错误", f"保存失败: {str(e)}")

            def cancel_and_close():
                """取消并关闭对话框"""
                edit_window.destroy()

            save_button = ctk.CTkButton(
                button_frame,
                text="💾 保存",
                command=save_and_close,
                width=120,
                height=40
            )
            save_button.pack(side="left", padx=10)

            cancel_button = ctk.CTkButton(
                button_frame,
                text="❌ 取消",
                command=cancel_and_close,
                width=120,
                height=40,
                fg_color="gray",
                hover_color="darkgray"
            )
            cancel_button.pack(side="left", padx=10)

        except Exception as e:
            messagebox.showerror("错误", f"打开编辑对话框失败: {str(e)}")

    def _show_cookie_help(self):
        """显示Cookie获取帮助"""
        try:
            # 创建帮助窗口
            help_window = ctk.CTkToplevel(self.parent_frame)
            help_window.title("Cookie获取帮助")
            help_window.geometry("700x800")
            help_window.transient(self.parent_frame.winfo_toplevel())
            help_window.grab_set()

            # 窗口居中
            help_window.update_idletasks()
            x = (help_window.winfo_screenwidth() // 2) - (700 // 2)
            y = (help_window.winfo_screenheight() // 2) - (800 // 2)
            help_window.geometry(f"700x800+{x}+{y}")

            # 标题
            title_label = ctk.CTkLabel(
                help_window,
                text="🍪 Cookie获取指南",
                font=ctk.CTkFont(size=24, weight="bold")
            )
            title_label.pack(pady=20)

            # 创建滚动文本框
            help_text = ctk.CTkTextbox(
                help_window,
                width=650,
                height=650,
                font=ctk.CTkFont(size=13)
            )
            help_text.pack(padx=20, pady=(0, 20), fill="both", expand=True)

            # 插入帮助内容
            help_content = """
🔍 什么是Cookie？
Cookie是网站存储在您浏览器中的小型数据文件，用于记住您的登录状态。

📋 获取Cookie的步骤：

方法一：使用Chrome浏览器
1. 登录AugmentCode
   • 打开浏览器，访问 https://augmentcode.com
   • 使用您的账号正常登录

2. 打开开发者工具
   • 按 F12 键，或右键点击页面选择"检查"

3. 切换到Network标签页
   • 在开发者工具中点击 "Network" 标签页
   • 如果看不到任何请求，请刷新页面 (F5)

4. 查找请求
   • 在请求列表中找到任意一个对 augmentcode.com 的请求
   • 点击该请求以查看详细信息

5. 复制Cookie
   • 在请求详情中找到 "Request Headers" 部分
   • 找到 Cookie: 行
   • 复制整个Cookie值（从冒号后面开始的所有内容）

方法二：使用Application标签页
1. 登录并打开开发者工具
   • 登录 https://augmentcode.com
   • 按 F12 打开开发者工具

2. 切换到Application标签页
   • 点击 "Application" 标签页
   • 在左侧面板中展开 "Storage" → "Cookies"
   • 点击 https://augmentcode.com

3. 复制Cookie值
   • 您会看到所有Cookie的列表
   • 手动复制重要的Cookie，格式如：
     name1=value1; name2=value2; name3=value3

⚠️ 注意事项：
• Cookie包含敏感信息，请不要分享给他人
• Cookie可能会过期，需要定期重新获取
• 确保您已经成功登录AugmentCode网站
• 复制完整的Cookie字符串

🔄 更新Cookie：
当Cookie过期时，重新登录AugmentCode网站，按照上述步骤获取新的Cookie，然后在工具中更新。

❓ 常见问题：
Q: Cookie多长时间会过期？
A: 通常几小时到几天不等，如果无法正常使用请重新获取。

Q: 为什么复制的Cookie无效？
A: 请确保已成功登录、复制了完整的Cookie字符串、格式正确。

Q: 如何知道Cookie是否有效？
A: 保存Cookie后，工具会自动检测登录状态。
"""

            help_text.insert("1.0", help_content)
            help_text.configure(state="disabled")  # 设为只读

            # 关闭按钮
            close_button = ctk.CTkButton(
                help_window,
                text="✅ 我知道了",
                command=help_window.destroy,
                width=120,
                height=40
            )
            close_button.pack(pady=10)

        except Exception as e:
            messagebox.showerror("错误", f"打开帮助窗口失败: {str(e)}")

    def _show_quick_login_dialog(self):
        """显示快速登录对话框（简化版）"""
        try:
            # 创建新窗口
            login_window = ctk.CTkToplevel(self.parent_frame)
            login_window.title("快速登录")
            login_window.geometry("450x400")
            login_window.transient(self.parent_frame.winfo_toplevel())
            login_window.grab_set()

            # 窗口居中
            login_window.update_idletasks()
            x = (login_window.winfo_screenwidth() // 2) - (450 // 2)
            y = (login_window.winfo_screenheight() // 2) - (400 // 2)
            login_window.geometry(f"450x400+{x}+{y}")

            # 标题
            title_label = ctk.CTkLabel(
                login_window,
                text="🚀 快速登录AugmentCode",
                font=ctk.CTkFont(size=20, weight="bold")
            )
            title_label.pack(pady=20)

            # 说明
            info_label = ctk.CTkLabel(
                login_window,
                text="请输入您的AugmentCode账号信息",
                font=ctk.CTkFont(size=14),
                text_color="gray"
            )
            info_label.pack(pady=(0, 20))

            # 邮箱输入
            email_label = ctk.CTkLabel(login_window, text="📧 邮箱地址:", font=ctk.CTkFont(size=14, weight="bold"))
            email_label.pack(anchor="w", padx=30, pady=(10, 5))

            email_entry = ctk.CTkEntry(
                login_window,
                placeholder_text="<EMAIL>",
                width=350,
                height=35,
                font=ctk.CTkFont(size=13)
            )
            email_entry.pack(padx=30, pady=(0, 15))

            # Cookie输入
            cookie_label = ctk.CTkLabel(login_window, text="🍪 Cookie字符串:", font=ctk.CTkFont(size=14, weight="bold"))
            cookie_label.pack(anchor="w", padx=30, pady=(10, 5))

            cookie_text = ctk.CTkTextbox(
                login_window,
                height=80,
                width=350,
                font=ctk.CTkFont(size=11)
            )
            cookie_text.pack(padx=30, pady=(0, 15))

            # 帮助提示
            help_label = ctk.CTkLabel(
                login_window,
                text="💡 不知道如何获取Cookie？点击下方帮助按钮",
                font=ctk.CTkFont(size=12),
                text_color="orange"
            )
            help_label.pack(pady=5)

            # 按钮区域
            button_frame = ctk.CTkFrame(login_window, fg_color="transparent")
            button_frame.pack(fill="x", padx=30, pady=20)

            def quick_save():
                """快速保存"""
                try:
                    email = email_entry.get().strip()
                    cookie_string = cookie_text.get("1.0", "end-1c").strip()

                    if not email:
                        messagebox.showerror("错误", "请输入邮箱地址")
                        return

                    if not cookie_string:
                        messagebox.showerror("错误", "请输入Cookie字符串")
                        return

                    # 保存登录信息（用户名使用邮箱前缀）
                    username = email.split('@')[0]
                    success = save_login_info(email, cookie_string, username)

                    if success:
                        messagebox.showinfo("成功", "登录信息已保存！")
                        login_window.destroy()
                        # 刷新主界面
                        threading.Thread(target=self._load_data, daemon=True).start()
                    else:
                        messagebox.showerror("错误", "保存登录信息失败")

                except Exception as e:
                    messagebox.showerror("错误", f"保存失败: {str(e)}")

            def show_help():
                """显示帮助"""
                self._show_cookie_help()

            # 保存按钮
            save_button = ctk.CTkButton(
                button_frame,
                text="💾 保存登录",
                command=quick_save,
                width=120,
                height=40,
                fg_color="#4CAF50",
                hover_color="#45a049",
                font=ctk.CTkFont(size=14, weight="bold")
            )
            save_button.pack(side="left", padx=5)

            # 帮助按钮
            help_button = ctk.CTkButton(
                button_frame,
                text="❓ 获取帮助",
                command=show_help,
                width=120,
                height=40,
                fg_color="blue",
                hover_color="darkblue"
            )
            help_button.pack(side="left", padx=5)

            # 取消按钮
            cancel_button = ctk.CTkButton(
                button_frame,
                text="❌ 取消",
                command=login_window.destroy,
                width=80,
                height=40,
                fg_color="gray",
                hover_color="darkgray"
            )
            cancel_button.pack(side="left", padx=5)

            # 焦点设置到邮箱输入框
            email_entry.focus()

        except Exception as e:
            messagebox.showerror("错误", f"打开快速登录对话框失败: {str(e)}")