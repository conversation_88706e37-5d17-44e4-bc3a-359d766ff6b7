#!/usr/bin/env python3
"""
命令行版本 - 完全复制原项目功能
用于验证所有原项目功能都已正确实现
"""

from utils.workspace_cleaner import WorkspaceCleaner

def main():
    """主函数 - 复制原项目的index.py功能"""
    cleaner = WorkspaceCleaner()
    
    print("System Paths:")
    print(f"Home Directory: {cleaner.paths.get('home', 'N/A')}")
    print(f"Storage Path: {cleaner.paths['storage_json']}")
    print(f"DB Path: {cleaner.paths['state_db']}")
    print(f"Machine ID Path: {cleaner.paths['machine_id']}")
    print(f"Workspace Storage Path: {cleaner.paths['workspace_storage']}")
    
    print("\nModifying Telemetry IDs:")
    try:
        result = cleaner.modify_telemetry_ids()
        print("\nBackup created at:")
        print(f"Storage backup path: {result['storage_backup_path']}")
        if result['machine_id_backup_path']:
            print(f"Machine ID backup path: {result['machine_id_backup_path']}")
        
        print("\nOld IDs:")
        print(f"Machine ID: {result['old_machine_id']}")
        print(f"Device ID: {result['old_device_id']}")
        
        print("\nNew IDs:")
        print(f"Machine ID: {result['new_machine_id']}")
        print(f"Device ID: {result['new_device_id']}")
        
        print("\nCleaning SQLite Database:")
        db_result = cleaner.clean_augment_database()
        print(f"Database backup created at: {db_result['db_backup_path']}")
        print(f"Deleted {db_result['deleted_rows']} rows containing 'augment' in their keys")
        
        print("\nCleaning Workspace Storage:")
        ws_result = cleaner.clean_workspace_storage()
        print(f"Workspace backup created at: {ws_result['backup_path']}")
        print(f"Deleted {ws_result['deleted_files_count']} files from workspace storage")
        
        print("\n🎉 Now you can run VS Code and login with the new email.")
        
    except FileNotFoundError as e:
        print(f"Error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")

if __name__ == "__main__":
    main()
