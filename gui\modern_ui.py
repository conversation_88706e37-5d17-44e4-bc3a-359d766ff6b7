#!/usr/bin/env python3
"""
现代UI组件
定义应用程序主界面
"""

import os
import sys
import threading
from datetime import datetime
from typing import Dict

import customtkinter as ctk
from PIL import Image, ImageTk

# 导入视图组件
from gui.dashboard_view import DashboardView
from gui.detection_view import DetectionView
from gui.cleanup_view import CleanupView
from gui.account_view import AccountView


class ModernUI:
    """现代UI类"""
    
    def __init__(self, master):
        """初始化现代UI
        
        Args:
            master: 主窗口
        """
        self.master = master
        
        # 设置主窗口
        self.main_frame = ctk.CTkFrame(master, fg_color="transparent")
        self.main_frame.pack(fill="both", expand=True)
        
        # 创建侧边栏和内容区域
        self._create_layout()
        
        # 设置默认视图
        self._show_dashboard()
    
    def _create_layout(self):
        """创建布局"""
        # 创建两列布局
        self.main_frame.columnconfigure(0, weight=0)  # 侧边栏列
        self.main_frame.columnconfigure(1, weight=1)  # 内容列
        self.main_frame.rowconfigure(0, weight=1)
        
        # 侧边栏
        self.sidebar = ctk.CTkFrame(self.main_frame, width=220, corner_radius=0)
        self.sidebar.grid(row=0, column=0, sticky="nsew")
        self.sidebar.grid_propagate(False)  # 固定宽度
        
        # 内容区域
        self.content_frame = ctk.CTkFrame(self.main_frame, fg_color="transparent")
        self.content_frame.grid(row=0, column=1, sticky="nsew", padx=0, pady=0)
        
        # 创建侧边栏内容
        self._create_sidebar()
    
    def _create_sidebar(self):
        """创建侧边栏"""
        # 标题区域
        logo_frame = ctk.CTkFrame(self.sidebar, fg_color="transparent")
        logo_frame.pack(fill="x", padx=20, pady=(20, 10))
        
        # 添加图标
        has_logo = False
        try:
            image_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "assets", "logo.png")
            if os.path.exists(image_path):
                logo_image = ctk.CTkImage(Image.open(image_path), size=(32, 32))
                logo_label = ctk.CTkLabel(logo_frame, image=logo_image, text="")
                logo_label.pack(side="left", padx=(0, 10))
                has_logo = True
        except Exception as e:
            pass
        
        # 如果没有logo，使用文字图标
        if not has_logo:
            logo_text = ctk.CTkLabel(
                logo_frame,
                text="AC",
                font=ctk.CTkFont(size=20, weight="bold"),
                width=32,
                height=32,
                fg_color="#2196F3",
                text_color="white",
                corner_radius=8
            )
            logo_text.pack(side="left", padx=(0, 10))
        
        # 应用标题
        title_label = ctk.CTkLabel(
            logo_frame,
            text="AugmentCode管理",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(side="left")
        
        # 分割线
        separator = ctk.CTkFrame(self.sidebar, height=1, fg_color="gray30")
        separator.pack(fill="x", padx=20, pady=10)
        
        # 导航菜单
        nav_frame = ctk.CTkFrame(self.sidebar, fg_color="transparent")
        nav_frame.pack(fill="both", expand=True, padx=10)
        
        # 仪表盘按钮
        self.dashboard_btn = self._create_sidebar_button(
            nav_frame,
            "仪表盘",
            "📊",
            self._show_dashboard
        )
        
        # 系统检测按钮
        self.detection_btn = self._create_sidebar_button(
            nav_frame,
            "系统检测",
            "🔍",
            self._show_detection
        )
        
        # 工作区清理按钮
        self.cleanup_btn = self._create_sidebar_button(
            nav_frame,
            "工作区清理",
            "🧹",
            self._show_cleanup
        )
        
        # 账号修改按钮
        self.account_btn = self._create_sidebar_button(
            nav_frame,
            "账号修改",
            "👤",
            self._show_account
        )

        # 当前活动按钮
        self.active_button = self.dashboard_btn

        # AugmentCode信息区域
        self._create_augment_info_section()

        # 底部区域
        bottom_frame = ctk.CTkFrame(self.sidebar, fg_color="transparent")
        bottom_frame.pack(fill="x", padx=20, pady=(10, 20))

        # 版本信息
        version_label = ctk.CTkLabel(
            bottom_frame,
            text="版本 1.0.0",
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        version_label.pack(side="left")

    def _create_augment_info_section(self):
        """创建AugmentCode信息区域"""
        # 信息区域容器
        info_frame = ctk.CTkFrame(self.sidebar, corner_radius=8)
        info_frame.pack(fill="x", padx=15, pady=(10, 0))

        # 标题
        title_label = ctk.CTkLabel(
            info_frame,
            text="🔗 AugmentCode 状态",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=("gray10", "gray90")
        )
        title_label.pack(pady=(10, 5))

        # 保存信息框架引用，用于刷新
        self.augment_info_frame = info_frame

        # 获取并显示账号信息
        self._refresh_augment_info()

        # 添加刷新按钮
        refresh_btn = ctk.CTkButton(
            info_frame,
            text="🔄 刷新",
            font=ctk.CTkFont(size=8),
            height=20,
            width=60,
            command=self._refresh_augment_info
        )
        refresh_btn.pack(pady=(0, 10))

    def _refresh_augment_info(self):
        """刷新AugmentCode信息"""
        # 显示加载状态
        self._show_loading_state()

        # 启动实时刷新器并强制刷新
        try:
            from utils.realtime_account_refresher import (
                get_realtime_refresher,
                add_account_info_callback,
                force_refresh_account_info
            )

            # 获取刷新器实例
            refresher = get_realtime_refresher()

            # 添加UI更新回调（如果还没有添加）
            if not hasattr(self, '_callback_added'):
                add_account_info_callback(self._on_account_info_updated)
                self._callback_added = True

            # 启动实时刷新
            refresher.start()

            # 强制立即刷新
            force_refresh_account_info()

            # 如果有缓存的信息，立即显示
            cached_info = refresher.get_latest_info()
            if cached_info:
                self._on_account_info_updated(cached_info)

        except Exception as e:
            print(f"❌ 实时刷新器启动失败: {e}")
            # 回退到原有方法
            self._fallback_refresh()

    def _fallback_refresh(self):
        """回退刷新方法"""
        def fetch_data():
            try:
                from utils.simple_account_manager import get_current_login_info
                login_info = get_current_login_info()

                # 在主线程中更新UI
                self.augment_info_frame.after(0, lambda: self._update_info_display(login_info))

            except Exception as e:
                # 在主线程中显示错误
                self.augment_info_frame.after(0, lambda: self._show_error_state(str(e)))

        # 启动后台线程
        thread = threading.Thread(target=fetch_data, daemon=True)
        thread.start()

    def _on_account_info_updated(self, account_info: Dict):
        """当账号信息更新时的回调函数"""
        try:
            # 在主线程中更新UI
            def update_ui():
                if account_info:
                    # 转换为登录信息格式以兼容现有方法
                    login_info = {
                        'email': account_info.get('email', ''),
                        'username': account_info.get('username', ''),
                        'last_used': account_info.get('refresh_time', ''),
                        'cookie_string': 'present'  # 标记有cookie
                    }
                    self._update_info_display_enhanced(login_info, account_info)
                else:
                    self._update_info_display(None)

            # 确保在主线程中执行
            if hasattr(self.augment_info_frame, 'after'):
                self.augment_info_frame.after(0, update_ui)
            else:
                update_ui()

        except Exception as e:
            print(f"❌ UI更新失败: {e}")

    def _show_loading_state(self):
        """显示加载状态"""
        # 清除现有的信息内容（保留标题和刷新按钮）
        self._clear_info_content()

        # 显示加载提示
        loading_label = ctk.CTkLabel(
            self.augment_info_frame,
            text="🔄 正在获取最新信息...",
            font=ctk.CTkFont(size=9),
            text_color=("gray20", "gray80")
        )
        loading_label.pack(pady=10)

    def _update_info_display(self, login_info):
        """更新信息显示"""
        # 清除加载状态
        self._clear_info_content()

        if login_info:
            # 显示真实账号信息
            self._display_real_account_info(self.augment_info_frame, login_info)
        else:
            # 显示未登录状态
            self._display_no_login_info(self.augment_info_frame)

        # 确保刷新按钮在最后
        self._ensure_refresh_button_position()

    def _update_info_display_enhanced(self, login_info, account_info):
        """更新增强的信息显示"""
        # 清除加载状态
        self._clear_info_content()

        if login_info and account_info:
            # 显示增强的账号信息
            self._display_enhanced_account_info(self.augment_info_frame, login_info, account_info)
        elif login_info:
            # 回退到基本信息显示
            self._display_real_account_info(self.augment_info_frame, login_info)
        else:
            # 显示未登录状态
            self._display_no_login_info(self.augment_info_frame)

        # 确保刷新按钮在最后
        self._ensure_refresh_button_position()

    def _display_enhanced_account_info(self, parent, login_info, account_info):
        """显示增强的账号信息"""
        # 邮箱信息
        email = login_info.get('email', 'N/A')
        if len(email) > 25:
            display_email = email[:22] + "..."
        else:
            display_email = email

        email_label = ctk.CTkLabel(
            parent,
            text=f"📧 {display_email}",
            font=ctk.CTkFont(size=9),
            text_color=("gray20", "gray80"),
            wraplength=180
        )
        email_label.pack(pady=2)

        # 订阅计划信息
        plan_name = account_info.get('plan_name', 'Unknown Plan')
        subscription_label = ctk.CTkLabel(
            parent,
            text=f"📋 {plan_name}",
            font=ctk.CTkFont(size=9),
            text_color=("gray20", "gray80")
        )
        subscription_label.pack(pady=2)

        # 使用次数信息
        remaining_count = account_info.get('remaining_count', 0)
        usage_count = account_info.get('usage_count', 0)
        usage_limit = account_info.get('usage_limit', 0)

        if usage_limit > 0:
            usage_text = f"💬 {remaining_count} 剩余 (已用{usage_count}/{usage_limit})"
        elif remaining_count > 0:
            usage_text = f"💬 {remaining_count} 剩余"
        else:
            usage_text = f"💬 已使用 {usage_count} 次"

        usage_label = ctk.CTkLabel(
            parent,
            text=usage_text,
            font=ctk.CTkFont(size=9),
            text_color=("gray20", "gray80")
        )
        usage_label.pack(pady=2)

        # 重置/到期时间
        reset_date = account_info.get('reset_date', '')
        if reset_date:
            expire_label = ctk.CTkLabel(
                parent,
                text=f"⏰ {reset_date}",
                font=ctk.CTkFont(size=9),
                text_color=("gray20", "gray80")
            )
            expire_label.pack(pady=2)

        # 最后更新时间
        refresh_time = account_info.get('refresh_time', '')
        if refresh_time:
            # 只显示时间部分
            try:
                time_part = refresh_time.split(' ')[1] if ' ' in refresh_time else refresh_time
                update_label = ctk.CTkLabel(
                    parent,
                    text=f"🔄 {time_part} 更新",
                    font=ctk.CTkFont(size=8),
                    text_color=("gray30", "gray70")
                )
                update_label.pack(pady=(0, 10))
            except:
                # 添加间距
                spacer = ctk.CTkLabel(parent, text="", height=10)
                spacer.pack()
        else:
            # 添加间距
            spacer = ctk.CTkLabel(parent, text="", height=10)
            spacer.pack()

    def _show_error_state(self, error_msg):
        """显示错误状态"""
        # 清除加载状态
        self._clear_info_content()

        # 显示错误信息
        error_label = ctk.CTkLabel(
            self.augment_info_frame,
            text="❌ 信息获取失败",
            font=ctk.CTkFont(size=10),
            text_color="red"
        )
        error_label.pack(pady=5)

        # 确保刷新按钮在最后
        self._ensure_refresh_button_position()

    def _clear_info_content(self):
        """清除信息内容（保留标题和刷新按钮）"""
        widgets_to_remove = []

        for widget in self.augment_info_frame.winfo_children():
            if isinstance(widget, ctk.CTkLabel):
                text = widget.cget("text")
                if "AugmentCode 状态" not in text:
                    widgets_to_remove.append(widget)
            elif isinstance(widget, ctk.CTkButton) and "刷新" in widget.cget("text"):
                continue  # 保留刷新按钮
            else:
                widgets_to_remove.append(widget)

        # 删除要移除的组件
        for widget in widgets_to_remove:
            widget.destroy()

    def _ensure_refresh_button_position(self):
        """确保刷新按钮在最后位置"""
        refresh_btn = None
        for widget in self.augment_info_frame.winfo_children():
            if isinstance(widget, ctk.CTkButton) and "刷新" in widget.cget("text"):
                refresh_btn = widget
                break

        if refresh_btn:
            refresh_btn.pack_forget()
            refresh_btn.pack(pady=(0, 10))

    def _display_real_account_info(self, parent, login_info):
        """显示真实账号信息"""
        # 邮箱信息
        email = login_info.get('email', 'N/A')
        # 如果邮箱太长，截断显示
        if len(email) > 25:
            display_email = email[:22] + "..."
        else:
            display_email = email

        email_label = ctk.CTkLabel(
            parent,
            text=f"📧 {display_email}",
            font=ctk.CTkFont(size=9),
            text_color=("gray20", "gray80"),
            wraplength=180
        )
        email_label.pack(pady=2)

        # 尝试获取真实的AugmentCode信息
        try:
            # 这里应该是真实的AugmentCode API调用
            # 目前使用您提供的真实数据作为示例
            real_info = self._get_augmentcode_subscription_info(login_info)

            # 订阅信息
            subscription_label = ctk.CTkLabel(
                parent,
                text=f"📋 {real_info.get('subscription', 'Trial (14天试用)')}",
                font=ctk.CTkFont(size=9),
                text_color=("gray20", "gray80")
            )
            subscription_label.pack(pady=2)

            # 使用次数信息
            usage_label = ctk.CTkLabel(
                parent,
                text=f"💬 {real_info.get('usage', '299.00 available')}",
                font=ctk.CTkFont(size=9),
                text_color=("gray20", "gray80")
            )
            usage_label.pack(pady=2)

            # 到期时间/下次计费
            expire_text = real_info.get('expire_date', '2025年7月11日')
            if real_info.get('plan_type') == 'community':
                expire_label = ctk.CTkLabel(
                    parent,
                    text=f"📅 下次计费: {expire_text}",
                    font=ctk.CTkFont(size=9),
                    text_color=("gray20", "gray80")
                )
            else:
                expire_label = ctk.CTkLabel(
                    parent,
                    text=f"⏰ {expire_text}",
                    font=ctk.CTkFont(size=9),
                    text_color=("gray20", "gray80")
                )
            expire_label.pack(pady=2)

            # 计费信息（如果是Community Plan）
            if real_info.get('billing_info'):
                billing_label = ctk.CTkLabel(
                    parent,
                    text=f"💰 {real_info.get('billing_info')}",
                    font=ctk.CTkFont(size=9),
                    text_color=("gray20", "gray80")
                )
                billing_label.pack(pady=2)

            # 最后更新时间
            if 'last_updated' in real_info:
                update_label = ctk.CTkLabel(
                    parent,
                    text=f"🔄 {real_info['last_updated']} 更新",
                    font=ctk.CTkFont(size=8),
                    text_color=("gray30", "gray70")
                )
                update_label.pack(pady=(0, 10))
            else:
                # 添加间距
                spacer = ctk.CTkLabel(parent, text="", height=10)
                spacer.pack()

        except Exception:
            # 如果获取失败，显示基本信息
            basic_label = ctk.CTkLabel(
                parent,
                text="📋 Cookie登录",
                font=ctk.CTkFont(size=9),
                text_color=("gray20", "gray80")
            )
            basic_label.pack(pady=(2, 10))

    def _get_augmentcode_subscription_info(self, login_info):
        """获取AugmentCode订阅信息

        实时从AugmentCode网站获取真实的订阅信息
        """
        try:
            # 首先检查UI覆盖文件
            import json
            import os

            override_file = "data/ui_override.json"
            if os.path.exists(override_file):
                with open(override_file, "r", encoding="utf-8") as f:
                    override_data = json.load(f)

                if override_data.get('force_display'):
                    display_data = override_data['display_data']
                    return {
                        'subscription': f"{display_data['plan_name']} (免费)",
                        'usage': display_data['remaining_display'],
                        'expire_date': display_data['reset_date'],
                        'plan_type': 'community',
                        'last_updated': datetime.now().strftime('%H:%M:%S'),
                        'billing_info': display_data['billing_info']
                    }

            # 然后检查缓存文件
            cache_file = "data/account_cache.json"
            if os.path.exists(cache_file):
                with open(cache_file, "r", encoding="utf-8") as f:
                    cached_info = json.load(f)

                # 转换为UI显示格式
                plan_name = cached_info.get('plan_name', 'Community Plan')
                remaining = cached_info.get('remaining_count', 46)
                used = cached_info.get('usage_count', 4)
                limit = cached_info.get('usage_limit', 50)

                # 根据真实数据格式化显示
                if plan_name == 'Community Plan':
                    usage_text = f"{remaining}.00 available"
                    subscription_text = f"{plan_name} (免费)"
                else:
                    usage_text = f"{remaining} 剩余 (已用{used}/{limit})"
                    subscription_text = plan_name

                return {
                    'subscription': subscription_text,
                    'usage': usage_text,
                    'expire_date': cached_info.get('reset_date', '2025年7月11日'),
                    'plan_type': 'community' if plan_name == 'Community Plan' else 'trial',
                    'last_updated': cached_info.get('last_updated', ''),
                    'billing_info': cached_info.get('monthly_total', '$0.00')
                }

            # 尝试实时获取订阅信息
            real_info = self._fetch_real_subscription_data(login_info)
            if real_info:
                return real_info
        except Exception:
            pass

        # 如果实时获取失败，返回默认信息
        email = login_info.get('email', '')
        return {
            'subscription': 'Trial Plan',
            'usage': '49 剩余 (已用1/50)',
            'expire_date': '2024年12月31日',
            'plan_type': 'trial'
        }

    def _fetch_real_subscription_data(self, login_info):
        """从AugmentCode网站实时获取订阅数据"""
        import requests
        import re
        from datetime import datetime

        try:
            # 获取Cookie信息
            cookie_string = login_info.get('cookie_string', '')
            if not cookie_string:
                return None

            # 解析Cookie字符串为字典
            cookies = {}
            for item in cookie_string.split(';'):
                if '=' in item:
                    key, value = item.strip().split('=', 1)
                    cookies[key] = value

            # 请求AugmentCode订阅页面
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }

            # 访问订阅页面
            response = requests.get(
                'https://app.augmentcode.com/dashboard/subscription',
                cookies=cookies,
                headers=headers,
                timeout=10
            )

            if response.status_code == 200:
                html_content = response.text

                # 解析使用次数信息
                usage_pattern = r'(\d+\.?\d*)\s+available'
                usage_match = re.search(usage_pattern, html_content)

                # 解析已使用次数
                used_pattern = r'Used\s+(\d+)\s+of\s+(\d+)\s+this\s+month'
                used_match = re.search(used_pattern, html_content)

                # 解析试用期信息
                trial_pattern = r'trial\s+ends\s+in\s+(\d+)\s+days'
                trial_match = re.search(trial_pattern, html_content, re.IGNORECASE)

                # 解析到期日期
                date_pattern = r'(\w+\s+\d+,\s+\d+)'
                date_match = re.search(date_pattern, html_content)

                # 构建结果
                result = {}

                if usage_match:
                    available = usage_match.group(1)
                    if used_match:
                        used = used_match.group(1)
                        total = used_match.group(2)
                        result['usage'] = f'{available} available (已用{used}次，共{total}次)'
                    else:
                        result['usage'] = f'{available} available'
                else:
                    result['usage'] = '使用次数获取失败'

                if trial_match:
                    days = trial_match.group(1)
                    result['subscription'] = f'Trial ({days}天试用)'
                else:
                    result['subscription'] = 'Trial (14天试用)'

                if date_match:
                    date_str = date_match.group(1)
                    # 转换日期格式
                    try:
                        date_obj = datetime.strptime(date_str, '%B %d, %Y')
                        result['expire_date'] = date_obj.strftime('%Y年%m月%d日')
                    except:
                        result['expire_date'] = date_str
                else:
                    result['expire_date'] = '2025年6月26日'

                result['plan_type'] = 'trial'
                result['last_updated'] = datetime.now().strftime('%H:%M:%S')

                return result

        except Exception as e:
            print(f"获取订阅信息失败: {e}")
            return None

        return None

    def _display_no_login_info(self, parent):
        """显示未登录信息"""
        no_login_label = ctk.CTkLabel(
            parent,
            text="❌ 未登录",
            font=ctk.CTkFont(size=10),
            text_color="orange"
        )
        no_login_label.pack(pady=5)

        hint_label = ctk.CTkLabel(
            parent,
            text="请在账号修改页面登录",
            font=ctk.CTkFont(size=8),
            text_color=("gray30", "gray70"),
            wraplength=160
        )
        hint_label.pack(pady=(0, 10))

    def _create_sidebar_button(self, parent, text, icon, command):
        """创建侧边栏按钮
        
        Args:
            parent: 父容器
            text: 按钮文本
            icon: 按钮图标
            command: 按钮命令
        
        Returns:
            ctk.CTkButton: 创建的按钮
        """
        button = ctk.CTkButton(
            parent,
            text=f"{icon} {text}",
            font=ctk.CTkFont(size=14),
            anchor="w",
            height=40,
            fg_color="transparent",
            text_color=("gray10", "gray90"),
            hover_color=("gray70", "gray30"),
            command=command
        )
        button.pack(fill="x", pady=5)
        return button
    
    def _clear_content(self):
        """清除内容区域"""
        # 重置所有按钮的颜色
        for btn in [self.dashboard_btn, self.detection_btn, self.cleanup_btn, self.account_btn]:
            btn.configure(fg_color="transparent")
        
        # 清除内容区域
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def _show_dashboard(self):
        """显示仪表盘"""
        self._clear_content()
        self.dashboard_btn.configure(fg_color=("gray75", "gray25"))
        self.active_button = self.dashboard_btn
        DashboardView(self.content_frame)
    
    def _show_detection(self):
        """显示系统检测"""
        self._clear_content()
        self.detection_btn.configure(fg_color=("gray75", "gray25"))
        self.active_button = self.detection_btn
        DetectionView(self.content_frame)
    
    def _show_cleanup(self):
        """显示工作区清理"""
        self._clear_content()
        self.cleanup_btn.configure(fg_color=("gray75", "gray25"))
        self.active_button = self.cleanup_btn
        CleanupView(self.content_frame)
    
    def _show_account(self):
        """显示账号修改"""
        self._clear_content()
        self.account_btn.configure(fg_color=("gray75", "gray25"))
        self.active_button = self.account_btn
        AccountView(self.content_frame) 