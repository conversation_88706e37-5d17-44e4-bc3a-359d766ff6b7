#!/usr/bin/env python3
"""
简单测试脚本
"""

import os
import sys

print("🔍 简单测试开始")
print(f"当前目录: {os.getcwd()}")
print(f"Python版本: {sys.version}")

# 测试基本导入
try:
    import json
    print("✅ json 导入成功")
except Exception as e:
    print(f"❌ json 导入失败: {e}")

try:
    import platform
    print("✅ platform 导入成功")
except Exception as e:
    print(f"❌ platform 导入失败: {e}")

# 测试路径模块
try:
    import utils.paths as paths
    print("✅ utils.paths 导入成功")
    
    app_data_dir = paths.get_app_data_dir()
    print(f"✅ 应用数据目录: {app_data_dir}")
    
except Exception as e:
    print(f"❌ utils.paths 导入失败: {e}")
    import traceback
    traceback.print_exc()

print("🎯 简单测试完成")
