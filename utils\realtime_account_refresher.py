#!/usr/bin/env python3
"""
实时账号信息刷新器
定期获取和更新AugmentCode账号信息
"""

import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Optional, Callable, List
import queue
import json
import os

from .simple_account_manager import get_current_login_info
from .subscription_checker import quick_check_subscription


class RealtimeAccountRefresher:
    """实时账号信息刷新器"""
    
    def __init__(self):
        self.refresh_interval = 300  # 5分钟刷新一次
        self.fast_refresh_interval = 60  # 快速刷新间隔（1分钟）
        self.is_running = False
        self.refresh_thread = None
        self.callbacks = []  # 回调函数列表
        self.last_account_info = {}
        self.error_count = 0
        self.max_errors = 5
        self.fast_refresh_mode = False
        self.fast_refresh_end_time = None
        
        # 线程安全的数据存储
        self._lock = threading.Lock()
        self._latest_info = {}
        self._last_update_time = None
        
    def add_callback(self, callback: Callable[[Dict], None]):
        """
        添加回调函数
        
        Args:
            callback: 当账号信息更新时调用的回调函数
        """
        if callback not in self.callbacks:
            self.callbacks.append(callback)
    
    def remove_callback(self, callback: Callable[[Dict], None]):
        """
        移除回调函数
        
        Args:
            callback: 要移除的回调函数
        """
        if callback in self.callbacks:
            self.callbacks.remove(callback)
    
    def start(self):
        """启动实时刷新"""
        if self.is_running:
            return
        
        self.is_running = True
        self.error_count = 0
        self.refresh_thread = threading.Thread(target=self._refresh_loop, daemon=True)
        self.refresh_thread.start()
        print("🔄 实时账号信息刷新器已启动")
    
    def stop(self):
        """停止实时刷新"""
        self.is_running = False
        if self.refresh_thread and self.refresh_thread.is_alive():
            self.refresh_thread.join(timeout=2)
        print("⏹️ 实时账号信息刷新器已停止")
    
    def enable_fast_refresh(self, duration_minutes: int = 10):
        """
        启用快速刷新模式
        
        Args:
            duration_minutes: 快速刷新持续时间（分钟）
        """
        self.fast_refresh_mode = True
        self.fast_refresh_end_time = datetime.now() + timedelta(minutes=duration_minutes)
        print(f"⚡ 快速刷新模式已启用，持续 {duration_minutes} 分钟")
    
    def force_refresh(self):
        """强制立即刷新"""
        if not self.is_running:
            self.start()
        
        # 在新线程中执行强制刷新，避免阻塞调用者
        def _force_refresh():
            try:
                self._perform_refresh()
            except Exception as e:
                print(f"❌ 强制刷新失败: {e}")
        
        thread = threading.Thread(target=_force_refresh, daemon=True)
        thread.start()
    
    def get_latest_info(self) -> Optional[Dict]:
        """
        获取最新的账号信息
        
        Returns:
            Dict: 最新的账号信息，如果没有则返回None
        """
        with self._lock:
            return self._latest_info.copy() if self._latest_info else None
    
    def get_last_update_time(self) -> Optional[datetime]:
        """
        获取最后更新时间
        
        Returns:
            datetime: 最后更新时间
        """
        with self._lock:
            return self._last_update_time
    
    def _refresh_loop(self):
        """刷新循环"""
        while self.is_running:
            try:
                # 执行刷新
                self._perform_refresh()
                
                # 重置错误计数
                self.error_count = 0
                
                # 确定下次刷新间隔
                if self.fast_refresh_mode:
                    if datetime.now() > self.fast_refresh_end_time:
                        self.fast_refresh_mode = False
                        print("⚡ 快速刷新模式已结束")
                        interval = self.refresh_interval
                    else:
                        interval = self.fast_refresh_interval
                else:
                    interval = self.refresh_interval
                
                # 等待下次刷新
                for _ in range(interval):
                    if not self.is_running:
                        break
                    time.sleep(1)
                
            except Exception as e:
                self.error_count += 1
                print(f"❌ 账号信息刷新失败 ({self.error_count}/{self.max_errors}): {e}")
                
                if self.error_count >= self.max_errors:
                    print("❌ 错误次数过多，停止自动刷新")
                    self.is_running = False
                    break
                
                # 错误时等待较短时间后重试
                time.sleep(30)
    
    def _perform_refresh(self):
        """执行刷新操作"""
        # 获取当前登录信息
        login_info = get_current_login_info()
        if not login_info:
            # 如果没有登录信息，清空缓存
            with self._lock:
                self._latest_info = {}
                self._last_update_time = datetime.now()
            
            # 通知回调函数
            self._notify_callbacks({})
            return
        
        # 获取订阅信息
        cookie_string = login_info.get('cookie_string')
        if not cookie_string:
            return
        
        # 尝试获取增强的账号信息
        account_info = self._get_enhanced_account_info(cookie_string)
        
        # 如果增强方法失败，使用原有方法
        if not account_info:
            success, sub_info = quick_check_subscription(cookie_string)
            if success:
                account_info = sub_info
        
        if account_info:
            # 添加基本登录信息
            account_info.update({
                'email': login_info.get('email', ''),
                'username': login_info.get('username', ''),
                'login_time': login_info.get('last_used', ''),
                'refresh_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
            
            # 检查信息是否有变化
            info_changed = self._has_info_changed(account_info)
            
            # 更新缓存
            with self._lock:
                self._latest_info = account_info
                self._last_update_time = datetime.now()
            
            # 如果信息有变化或者是强制刷新，通知回调函数
            if info_changed:
                self._notify_callbacks(account_info)
                print(f"✅ 账号信息已更新: {account_info.get('email', 'Unknown')}")
    
    def _get_enhanced_account_info(self, cookie_string: str) -> Optional[Dict]:
        """获取增强的账号信息"""
        try:
            # 尝试导入增强方法
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.dirname(__file__)))
            
            from enhanced_account_methods import get_enhanced_account_info
            
            success, account_info = get_enhanced_account_info(cookie_string)
            if success:
                return account_info
        except ImportError:
            pass
        except Exception as e:
            print(f"⚠️ 增强账号信息获取失败: {e}")
        
        return None
    
    def _has_info_changed(self, new_info: Dict) -> bool:
        """检查账号信息是否有变化"""
        if not self.last_account_info:
            self.last_account_info = new_info.copy()
            return True
        
        # 比较关键字段
        key_fields = [
            'remaining_count', 'usage_count', 'usage_limit', 
            'plan_name', 'subscription_status', 'reset_date'
        ]
        
        for field in key_fields:
            if self.last_account_info.get(field) != new_info.get(field):
                self.last_account_info = new_info.copy()
                return True
        
        return False
    
    def _notify_callbacks(self, account_info: Dict):
        """通知所有回调函数"""
        for callback in self.callbacks[:]:  # 创建副本以避免并发修改
            try:
                callback(account_info)
            except Exception as e:
                print(f"⚠️ 回调函数执行失败: {e}")


# 全局实例
_refresher = None


def get_realtime_refresher() -> RealtimeAccountRefresher:
    """获取实时刷新器实例"""
    global _refresher
    if _refresher is None:
        _refresher = RealtimeAccountRefresher()
    return _refresher


def start_realtime_refresh():
    """启动实时刷新"""
    refresher = get_realtime_refresher()
    refresher.start()


def stop_realtime_refresh():
    """停止实时刷新"""
    refresher = get_realtime_refresher()
    refresher.stop()


def force_refresh_account_info():
    """强制刷新账号信息"""
    refresher = get_realtime_refresher()
    refresher.force_refresh()


def add_account_info_callback(callback: Callable[[Dict], None]):
    """添加账号信息更新回调"""
    refresher = get_realtime_refresher()
    refresher.add_callback(callback)


def remove_account_info_callback(callback: Callable[[Dict], None]):
    """移除账号信息更新回调"""
    refresher = get_realtime_refresher()
    refresher.remove_callback(callback)
