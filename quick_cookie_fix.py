#!/usr/bin/env python3
"""
快速Cookie修复工具
"""

def quick_fix():
    """快速修复Cookie"""
    print("🚀 快速修复Cookie...")
    
    # 您从浏览器获取的Cookie
    new_cookie = "_session=**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.WHZ6bTR0eV80ckNEVDI5NGIzRkNSHciLCJ1c2VyX2lkIjoiNjc1YTcyOWQtYTcyZC00OWMzLWI1OWYtZDcyOWRhNzJkNDljIiwiZW1haWwiOiJvcGdnb3p5ZHVjQHl1bi5waWNzIiwibmFtZSI6Im9wZ2dvenlkdWMiLCJhdmF0YXIiOm51bGwsInByb3ZpZGVyIjoiYXV0aDAiLCJwcm92aWRlcl9pZCI6ImF1dGgwfDY2N2Y1YTcyOWRhNzJkNDljM2I1OWYiLCJjcmVhdGVkX2F0IjoiMjAyNC0wNi0yOFQxNDo1MDoxNy4wMDBaIiwidXBkYXRlZF9hdCI6IjIwMjQtMDYtMjhUMTQ6NTA6MTcuMDAwWiIsImxhc3RfbG9naW4iOiIyMDI0LTEyLTEyVDAxOjIzOjQ5LjAwMFoiLCJsb2dpbl9jb3VudCI6MiwiYmxvY2tlZCI6ZmFsc2UsImVtYWlsX3ZlcmlmaWVkIjp0cnVlLCJwaWN0dXJlIjoiaHR0cHM6Ly9zLmdyYXZhdGFyLmNvbS9hdmF0YXIvZTcyZDcyZDcyZDcyZDcyZDcyZDcyZDcyZDcyZDcyZDc/cz00ODAmcj1wZyZkPWh0dHBzJTNBJTJGJTJGY2RuLmF1dGgwLmNvbSUyRmF2YXRhcnMlMkZvcC5wbmciLCJ1c2VybmFtZSI6Im9wZ2dvenlkdWMiLCJnaXZlbl9uYW1lIjoib3BnZ296eWR1YyIsImZhbWlseV9uYW1lIjoiIiwibmlja25hbWUiOiJvcGdnb3p5ZHVjIiwibG9jYWxlIjoiZW4iLCJ1cGRhdGVkX2F0IjoiMjAyNC0xMi0xMlQwMToyMzo0OS4wMDBaIiwic3ViIjoiYXV0aDB8NjY3ZjVhNzI5ZGE3MmQ0OWMzYjU5ZiIsImF1ZCI6WyJodHRwczovL2FwcC5hdWdtZW50Y29kZS5jb20iLCJodHRwczovL2F1Z21lbnRjb2RlLmF1dGgwLmNvbS91c2VyaW5mbyJdLCJpYXQiOjE3MzM5NjkwMjksImV4cCI6MTczNDAwNTAyOSwiYXpwIjoid2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGEiLCJzY29wZSI6Im9wZW5pZCBlbWFpbCBwcm9maWxlIn0"
    
    try:
        # 直接修改配置文件
        import json
        import os
        
        config_file = "data/login_info.json"
        
        # 确保目录存在
        os.makedirs("data", exist_ok=True)
        
        # 创建或更新配置
        login_data = {
            "email": "<EMAIL>",
            "username": "opggozyduc", 
            "cookie_string": new_cookie,
            "last_used": "2024-12-12 01:23:49"
        }
        
        with open(config_file, "w", encoding="utf-8") as f:
            json.dump(login_data, f, indent=2, ensure_ascii=False)
        
        print("✅ Cookie已直接更新到配置文件")
        return True
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        return False

if __name__ == "__main__":
    quick_fix()
