#!/usr/bin/env python3
"""
测试手动登录功能
演示如何输入账号和Cookie信息
"""

from utils.account_manager import save_login_info, get_current_login_info, clear_login_info
from utils.account_detector import get_all_account_info, format_account_summary

def test_manual_login():
    """测试手动登录功能"""
    print("🔍 测试手动登录功能")
    print("=" * 60)
    
    # 1. 检查当前状态
    print("\n1️⃣ 检查当前登录状态:")
    current_info = get_current_login_info()
    if current_info:
        print(f"   ✅ 已有登录信息:")
        print(f"   📧 邮箱: {current_info.get('email')}")
        print(f"   👤 用户名: {current_info.get('username')}")
        print(f"   🕒 最后使用: {current_info.get('last_used')}")
        print(f"   🍪 Cookie长度: {len(current_info.get('cookie_string', ''))} 字符")
    else:
        print("   ❌ 未找到登录信息")
    
    # 2. 演示保存登录信息
    print("\n2️⃣ 演示保存登录信息:")
    demo_email = "<EMAIL>"
    demo_username = "演示用户"
    demo_cookie = "session=demo_session_token; user=demo_user_id; auth=demo_auth_token"
    
    print(f"   保存演示账号: {demo_email}")
    success = save_login_info(demo_email, demo_cookie, demo_username)
    if success:
        print("   ✅ 保存成功")
    else:
        print("   ❌ 保存失败")
    
    # 3. 验证保存的信息
    print("\n3️⃣ 验证保存的信息:")
    saved_info = get_current_login_info()
    if saved_info:
        print(f"   ✅ 验证成功:")
        print(f"   📧 邮箱: {saved_info.get('email')}")
        print(f"   👤 用户名: {saved_info.get('username')}")
        print(f"   🍪 Cookie: {saved_info.get('cookie_string')[:50]}...")
    else:
        print("   ❌ 验证失败")
    
    # 4. 测试账号检测
    print("\n4️⃣ 测试账号检测:")
    account_info = get_all_account_info()
    augment_account = account_info.get('augment_account', {})
    
    print(f"   登录状态: {'✅ 已登录' if augment_account.get('logged_in') else '❌ 未登录'}")
    if augment_account.get('logged_in'):
        print(f"   📧 检测到邮箱: {augment_account.get('email')}")
        print(f"   👤 检测到用户名: {augment_account.get('username')}")
        print(f"   🔑 登录方式: {augment_account.get('login_method')}")
        
        cookie_info = augment_account.get('cookie_info', {})
        if cookie_info.get('has_cookie'):
            print(f"   🍪 Cookie状态: 已保存 ({cookie_info.get('cookie_length')} 字符)")
    
    # 5. 显示完整的账号摘要
    print("\n5️⃣ 完整账号摘要:")
    summary = format_account_summary(account_info)
    print(summary)
    
    # 6. 清理演示数据（可选）
    print(f"\n6️⃣ 清理演示数据:")
    choice = input("是否清除演示登录信息？(y/N): ").strip().lower()
    if choice == 'y':
        success = clear_login_info()
        if success:
            print("   ✅ 清除成功")
        else:
            print("   ❌ 清除失败")
    else:
        print("   ℹ️ 保留演示数据")
    
    print("\n" + "=" * 60)
    print("🎉 手动登录功能测试完成！")
    print("\n💡 使用说明:")
    print("1. 在GUI的'账号管理'页面输入您的真实邮箱和Cookie")
    print("2. Cookie可以从浏览器的开发者工具中获取")
    print("3. 登录 https://augmentcode.com 后按F12，在Network标签页找到Cookie")
    print("4. 保存后系统将自动识别您的登录状态")

if __name__ == "__main__":
    test_manual_login()
