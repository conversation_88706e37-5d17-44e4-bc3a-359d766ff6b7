#!/usr/bin/env python3
"""
AugmentCode账号管理工具
高级图形界面版本
"""

import os
import sys
import threading
import customtkinter as ctk

from gui.modern_ui import ModernUI


def initialize_assets():
    """初始化资源文件"""
    # 确保assets目录存在
    assets_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "assets")
    if not os.path.exists(assets_dir):
        os.makedirs(assets_dir)


def main():
    """主函数"""
    # 初始化资源
    initialize_assets()

    # 设置UI主题
    ctk.set_appearance_mode("dark")  # 设置外观模式为深色
    ctk.set_default_color_theme("blue")  # 设置默认颜色主题为蓝色

    # 创建主窗口
    app = ctk.CTk()
    app.title("AugmentCode管理工具")
    app.geometry("900x600")
    app.minsize(800, 500)

    # 初始化实时账号信息刷新器
    try:
        from utils.realtime_account_refresher import start_realtime_refresh
        start_realtime_refresh()
        print("✅ 实时账号信息刷新器已启动")
    except Exception as e:
        print(f"⚠️ 实时账号信息刷新器启动失败: {e}")

    # 创建现代UI
    ModernUI(app)

    # 设置窗口关闭事件处理
    def on_closing():
        try:
            from utils.realtime_account_refresher import stop_realtime_refresh
            stop_realtime_refresh()
            print("✅ 实时账号信息刷新器已停止")
        except:
            pass
        app.destroy()

    app.protocol("WM_DELETE_WINDOW", on_closing)

    # 启动应用
    app.mainloop()


if __name__ == "__main__":
    main() 