#!/usr/bin/env python3
"""
AugmentCode管理工具 - 极简版
"""

import os
import customtkinter as ctk

class SimpleApp:
    def __init__(self, master):
        self.master = master
        
        # 设置主窗口
        self.main_frame = ctk.CTkFrame(master, fg_color="transparent")
        self.main_frame.pack(fill="both", expand=True)
        
        # 创建两列布局
        self.main_frame.columnconfigure(0, weight=0)  # 侧边栏列
        self.main_frame.columnconfigure(1, weight=1)  # 内容列
        self.main_frame.rowconfigure(0, weight=1)
        
        # 侧边栏
        self.sidebar = ctk.CTkFrame(self.main_frame, width=220, corner_radius=0)
        self.sidebar.grid(row=0, column=0, sticky="nsew")
        self.sidebar.grid_propagate(False)  # 固定宽度
        
        # 内容区域
        self.content_frame = ctk.CTkFrame(self.main_frame, fg_color="transparent")
        self.content_frame.grid(row=0, column=1, sticky="nsew", padx=0, pady=0)
        
        # 创建侧边栏内容
        self._create_sidebar()
        
        # 显示欢迎页面
        self._show_welcome()
    
    def _create_sidebar(self):
        # 标题区域
        logo_frame = ctk.CTkFrame(self.sidebar, fg_color="transparent")
        logo_frame.pack(fill="x", padx=20, pady=(20, 10))
        
        # 文字图标
        logo_text = ctk.CTkLabel(
            logo_frame,
            text="AC",
            font=ctk.CTkFont(size=20, weight="bold"),
            width=32,
            height=32,
            fg_color="#2196F3",
            text_color="white",
            corner_radius=8
        )
        logo_text.pack(side="left", padx=(0, 10))
        
        # 应用标题
        title_label = ctk.CTkLabel(
            logo_frame,
            text="AugmentCode管理",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(side="left")
        
        # 分割线
        separator = ctk.CTkFrame(self.sidebar, height=1, fg_color="gray30")
        separator.pack(fill="x", padx=20, pady=10)
        
        # 导航菜单
        nav_frame = ctk.CTkFrame(self.sidebar, fg_color="transparent")
        nav_frame.pack(fill="both", expand=True, padx=10)
        
        # 欢迎按钮
        self.welcome_btn = self._create_sidebar_button(
            nav_frame, "欢迎", "👋", self._show_welcome
        )
        
        # 关于按钮
        self.about_btn = self._create_sidebar_button(
            nav_frame, "关于", "ℹ️", self._show_about
        )
        
        # 版本信息
        bottom_frame = ctk.CTkFrame(self.sidebar, fg_color="transparent")
        bottom_frame.pack(fill="x", padx=20, pady=20)
        
        version_label = ctk.CTkLabel(
            bottom_frame,
            text="简易版本 1.0",
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        version_label.pack(side="left")
    
    def _create_sidebar_button(self, parent, text, icon, command):
        button = ctk.CTkButton(
            parent,
            text=f"{icon} {text}",
            font=ctk.CTkFont(size=14),
            anchor="w",
            height=40,
            fg_color="transparent",
            text_color=("gray10", "gray90"),
            hover_color=("gray70", "gray30"),
            command=command
        )
        button.pack(fill="x", pady=5)
        return button
    
    def _clear_content(self):
        # 重置所有按钮的颜色
        for btn in [self.welcome_btn, self.about_btn]:
            btn.configure(fg_color="transparent")
        
        # 清除内容区域
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def _show_welcome(self):
        self._clear_content()
        self.welcome_btn.configure(fg_color=("gray75", "gray25"))
        
        # 创建欢迎页面
        welcome_frame = ctk.CTkFrame(self.content_frame, fg_color="transparent")
        welcome_frame.pack(fill="both", expand=True, padx=30, pady=30)
        
        # 欢迎标题
        welcome_title = ctk.CTkLabel(
            welcome_frame,
            text="欢迎使用 AugmentCode 管理工具",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        welcome_title.pack(pady=(50, 20))
        
        # 欢迎信息
        welcome_msg = ctk.CTkLabel(
            welcome_frame,
            text="这是一个简化版本的AugmentCode管理工具界面\n用于展示现代化UI设计",
            font=ctk.CTkFont(size=16),
            justify="center"
        )
        welcome_msg.pack(pady=20)
        
        # 卡片区域
        cards_frame = ctk.CTkFrame(welcome_frame, fg_color="transparent")
        cards_frame.pack(fill="x", pady=30)
        
        cards_frame.columnconfigure(0, weight=1)
        cards_frame.columnconfigure(1, weight=1)
        
        # 示例卡片1
        card1 = ctk.CTkFrame(cards_frame)
        card1.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
        
        card1_title = ctk.CTkLabel(
            card1,
            text="简洁设计",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        card1_title.pack(pady=15)
        
        card1_text = ctk.CTkLabel(
            card1,
            text="现代化界面，简洁易用",
            font=ctk.CTkFont(size=14)
        )
        card1_text.pack(pady=10, padx=20)
        
        # 示例卡片2
        card2 = ctk.CTkFrame(cards_frame)
        card2.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")
        
        card2_title = ctk.CTkLabel(
            card2,
            text="高效操作",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        card2_title.pack(pady=15)
        
        card2_text = ctk.CTkLabel(
            card2,
            text="快速响应，高效处理",
            font=ctk.CTkFont(size=14)
        )
        card2_text.pack(pady=10, padx=20)
    
    def _show_about(self):
        self._clear_content()
        self.about_btn.configure(fg_color=("gray75", "gray25"))
        
        # 创建关于页面
        about_frame = ctk.CTkFrame(self.content_frame, fg_color="transparent")
        about_frame.pack(fill="both", expand=True, padx=30, pady=30)
        
        # 关于标题
        about_title = ctk.CTkLabel(
            about_frame,
            text="关于本应用",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        about_title.pack(pady=(50, 20))
        
        # 关于信息
        about_info = ctk.CTkTextbox(about_frame, height=200)
        about_info.pack(fill="x", pady=20, padx=20)
        about_info.insert("1.0", """AugmentCode管理工具 - 极简版

这是一个用于展示现代化UI设计的演示应用程序。
该应用使用了CustomTkinter库构建，展示了如何创建美观、现代的桌面应用界面。

特点：
• 深色主题
• 响应式布局
• 卡片式设计
• 简洁的导航栏

版本: 1.0
""")
        about_info.configure(state="disabled")


def main():
    # 初始化资源
    assets_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "assets")
    if not os.path.exists(assets_dir):
        os.makedirs(assets_dir)
    
    # 设置UI主题
    ctk.set_appearance_mode("dark")  # 设置外观模式为深色
    ctk.set_default_color_theme("blue")  # 设置默认颜色主题为蓝色
    
    # 创建主窗口
    app = ctk.CTk()
    app.title("AugmentCode管理工具")
    app.geometry("900x600")
    app.minsize(800, 500)
    
    # 创建简易UI
    SimpleApp(app)
    
    # 启动应用
    app.mainloop()


if __name__ == "__main__":
    main() 