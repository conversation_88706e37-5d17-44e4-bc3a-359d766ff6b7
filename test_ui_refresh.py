#!/usr/bin/env python3
"""
测试UI刷新功能
"""

from utils.simple_account_manager import save_login_info, get_current_login_info, clear_login_info
from utils.account_detector import get_all_account_info

def test_ui_refresh():
    """测试UI刷新功能"""
    print("🔧 测试UI刷新功能")
    print("=" * 50)
    
    # 1. 保存测试账号信息
    print("\n1. 保存测试账号信息:")
    test_email = "<EMAIL>"
    test_cookie = "session=ui_test_123; user=ui_test_456"
    test_username = "UI测试用户"
    
    save_result = save_login_info(test_email, test_cookie, test_username)
    print(f"保存结果: {'✅ 成功' if save_result else '❌ 失败'}")
    
    # 2. 验证手动登录信息
    print("\n2. 验证手动登录信息:")
    manual_info = get_current_login_info()
    if manual_info:
        print("✅ 手动登录信息存在:")
        print(f"   邮箱: {manual_info.get('email')}")
        print(f"   用户名: {manual_info.get('username')}")
    else:
        print("❌ 手动登录信息不存在")
        return False
    
    # 3. 测试账号检测器
    print("\n3. 测试账号检测器:")
    account_info = get_all_account_info()
    augment_account = account_info.get('augment_account', {})
    
    print(f"检测到的登录状态: {'✅ 已登录' if augment_account.get('logged_in') else '❌ 未登录'}")
    if augment_account.get('logged_in'):
        print(f"检测到的邮箱: {augment_account.get('email')}")
        print(f"检测到的用户名: {augment_account.get('username')}")
    
    # 4. 测试UI判断逻辑
    print("\n4. 测试UI判断逻辑:")
    manual_login_info = get_current_login_info()
    should_show_logged_in = manual_login_info or augment_account.get('logged_in', False)
    
    print(f"手动登录信息存在: {'✅ 是' if manual_login_info else '❌ 否'}")
    print(f"自动检测登录状态: {'✅ 是' if augment_account.get('logged_in') else '❌ 否'}")
    print(f"应该显示已登录界面: {'✅ 是' if should_show_logged_in else '❌ 否'}")
    
    if should_show_logged_in:
        print("✅ UI应该显示已登录状态")
        return True
    else:
        print("❌ UI仍会显示未登录状态")
        return False

if __name__ == "__main__":
    success = test_ui_refresh()
    
    if success:
        print("\n🎉 UI刷新逻辑修复成功！")
        print("\n📋 现在请:")
        print("1. 在GUI中点击'🔄 刷新状态'按钮")
        print("2. 或者重新启动GUI")
        print("3. 应该能看到正确的已登录界面")
    else:
        print("\n❌ UI刷新逻辑还有问题")
