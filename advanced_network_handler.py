#!/usr/bin/env python3
"""
高级网络处理器
解决网络连接问题和反爬虫机制
"""

import requests
import time
import random
import json
from datetime import datetime, timedelta
from typing import Dict, Optional, List, Tuple
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class AdvancedNetworkHandler:
    """高级网络处理器"""
    
    def __init__(self):
        self.session = None
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        self.base_url = "https://app.augmentcode.com"
        self.request_count = 0
        self.last_request_time = 0
        self.min_delay = 2  # 最小请求间隔（秒）
        self.max_delay = 5  # 最大请求间隔（秒）
        
        self._setup_session()
    
    def _setup_session(self):
        """设置会话"""
        self.session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )
        
        # 配置HTTP适配器
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=10,
            pool_maxsize=20,
            pool_block=False
        )
        
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # 设置基本配置
        self.session.verify = True
        self.session.timeout = (10, 30)  # 连接超时10秒，读取超时30秒
        
        # 设置默认请求头
        self._update_headers()
    
    def _update_headers(self):
        """更新请求头"""
        user_agent = random.choice(self.user_agents)
        
        headers = {
            'User-Agent': user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        }
        
        self.session.headers.update(headers)
    
    def _wait_between_requests(self):
        """请求间等待"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_delay:
            delay = random.uniform(self.min_delay, self.max_delay)
            time.sleep(delay)
        
        self.last_request_time = time.time()
        self.request_count += 1
    
    def setup_cookies(self, cookie_string: str) -> bool:
        """设置Cookie"""
        try:
            cookies = self._parse_cookie_string(cookie_string)
            if not cookies:
                return False
            
            # 清除现有cookies
            self.session.cookies.clear()
            
            # 设置cookies
            for key, value in cookies.items():
                self.session.cookies.set(key, value, domain='.augmentcode.com')
            
            print(f"✅ 已设置 {len(cookies)} 个Cookie")
            return True
            
        except Exception as e:
            print(f"❌ Cookie设置失败: {e}")
            return False
    
    def _parse_cookie_string(self, cookie_string: str) -> Dict[str, str]:
        """解析Cookie字符串"""
        cookies = {}
        
        try:
            if ';' in cookie_string:
                parts = cookie_string.split(';')
                for part in parts:
                    part = part.strip()
                    if '=' in part:
                        key, value = part.split('=', 1)
                        cookies[key.strip()] = value.strip()
            elif '=' in cookie_string:
                key, value = cookie_string.split('=', 1)
                cookies[key.strip()] = value.strip()
        except Exception:
            pass
            
        return cookies
    
    def safe_request(self, url: str, method: str = 'GET', **kwargs) -> Optional[requests.Response]:
        """安全的网络请求"""
        self._wait_between_requests()
        
        # 每10个请求更新一次请求头
        if self.request_count % 10 == 0:
            self._update_headers()
        
        # 设置Referer
        if 'headers' not in kwargs:
            kwargs['headers'] = {}
        
        if '/dashboard' in url and 'Referer' not in kwargs['headers']:
            kwargs['headers']['Referer'] = self.base_url
        
        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                print(f"🔄 请求 {url} (第{attempt + 1}次)")
                
                if method.upper() == 'GET':
                    response = self.session.get(url, **kwargs)
                elif method.upper() == 'POST':
                    response = self.session.post(url, **kwargs)
                else:
                    response = self.session.request(method, url, **kwargs)
                
                if response.status_code in [200, 302]:
                    print(f"✅ 请求成功: {response.status_code}")
                    return response
                elif response.status_code == 429:
                    print("⚠️ 请求过于频繁，等待更长时间...")
                    time.sleep(random.uniform(10, 20))
                elif response.status_code in [401, 403]:
                    print(f"❌ 访问被拒绝: {response.status_code}")
                    break
                else:
                    print(f"⚠️ 状态码: {response.status_code}")
                
            except requests.exceptions.ConnectionError as e:
                if "10054" in str(e):
                    print(f"❌ 连接被重置 (第{attempt + 1}次)")
                    if attempt < max_attempts - 1:
                        delay = random.uniform(5, 15) * (attempt + 1)
                        print(f"⏳ 等待 {delay:.1f} 秒后重试...")
                        time.sleep(delay)
                        # 重新创建会话
                        self._setup_session()
                        if hasattr(self, '_last_cookies'):
                            self.setup_cookies(self._last_cookies)
                else:
                    print(f"❌ 连接错误: {e}")
                    
            except requests.exceptions.Timeout as e:
                print(f"⏰ 请求超时 (第{attempt + 1}次): {e}")
                if attempt < max_attempts - 1:
                    time.sleep(random.uniform(3, 8))
                    
            except Exception as e:
                print(f"❌ 未知错误 (第{attempt + 1}次): {e}")
                if attempt < max_attempts - 1:
                    time.sleep(random.uniform(2, 5))
        
        print(f"❌ 请求失败，已尝试 {max_attempts} 次")
        return None
    
    def get_page_content(self, path: str) -> Optional[str]:
        """获取页面内容"""
        url = self.base_url + path
        response = self.safe_request(url)
        
        if response and response.status_code == 200:
            return response.text
        
        return None
    
    def test_connection(self) -> Dict:
        """测试连接"""
        print("🧪 测试网络连接...")
        
        results = {
            'basic_test': False,
            'augment_test': False,
            'cookie_test': False,
            'details': {}
        }
        
        # 基本连接测试
        try:
            response = self.safe_request("https://httpbin.org/get")
            results['basic_test'] = response is not None and response.status_code == 200
        except:
            pass
        
        # AugmentCode连接测试
        try:
            response = self.safe_request(self.base_url)
            results['augment_test'] = response is not None
            if response:
                results['details']['augment_status'] = response.status_code
        except:
            pass
        
        return results


# 全局实例
_network_handler = None


def get_network_handler() -> AdvancedNetworkHandler:
    """获取网络处理器实例"""
    global _network_handler
    if _network_handler is None:
        _network_handler = AdvancedNetworkHandler()
    return _network_handler


def safe_get_page(path: str, cookie_string: str = None) -> Optional[str]:
    """安全获取页面内容"""
    handler = get_network_handler()
    
    if cookie_string:
        handler._last_cookies = cookie_string
        handler.setup_cookies(cookie_string)
    
    return handler.get_page_content(path)
