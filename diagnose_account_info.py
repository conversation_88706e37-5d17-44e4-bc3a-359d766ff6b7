#!/usr/bin/env python3
"""
账号信息获取诊断工具
专门诊断使用次数获取问题
"""

import requests
import re
import json
from datetime import datetime
from typing import Dict, Optional


def diagnose_cookie_validity():
    """诊断Cookie有效性"""
    print("🍪 诊断Cookie有效性...")
    
    try:
        from utils.simple_account_manager import get_current_login_info
        login_info = get_current_login_info()
        
        if not login_info:
            print("❌ 未找到登录信息")
            return False
        
        cookie_string = login_info.get('cookie_string', '')
        email = login_info.get('email', '')
        
        print(f"📧 账号: {email}")
        print(f"🍪 Cookie长度: {len(cookie_string)} 字符")
        
        # 解析Cookie
        cookies = {}
        if ';' in cookie_string:
            for item in cookie_string.split(';'):
                if '=' in item:
                    key, value = item.strip().split('=', 1)
                    cookies[key] = value
        
        print(f"🔑 Cookie数量: {len(cookies)}")
        for key in cookies.keys():
            print(f"   - {key}")
        
        return True
        
    except Exception as e:
        print(f"❌ Cookie诊断失败: {e}")
        return False


def test_direct_page_access():
    """直接测试页面访问"""
    print("\n🌐 测试直接页面访问...")
    
    try:
        from utils.simple_account_manager import get_current_login_info
        login_info = get_current_login_info()
        
        if not login_info:
            print("❌ 未找到登录信息")
            return {}
        
        cookie_string = login_info.get('cookie_string', '')
        
        # 解析Cookie
        cookies = {}
        if ';' in cookie_string:
            for item in cookie_string.split(';'):
                if '=' in item:
                    key, value = item.strip().split('=', 1)
                    cookies[key] = value
        
        session = requests.Session()
        for key, value in cookies.items():
            session.cookies.set(key, value)
        
        # 设置请求头
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://app.augmentcode.com/'
        })
        
        # 测试不同的页面
        test_urls = [
            ('主页', 'https://app.augmentcode.com/'),
            ('仪表板', 'https://app.augmentcode.com/dashboard'),
            ('订阅页面', 'https://app.augmentcode.com/dashboard/subscription'),
            ('社区页面', 'https://app.augmentcode.com/community'),
            ('账单页面', 'https://app.augmentcode.com/billing'),
            ('设置页面', 'https://app.augmentcode.com/settings')
        ]
        
        results = {}
        
        for name, url in test_urls:
            try:
                print(f"🔄 测试 {name}: {url}")
                response = session.get(url, timeout=15)
                
                results[name] = {
                    'status_code': response.status_code,
                    'content_length': len(response.content),
                    'success': response.status_code in [200, 302]
                }
                
                status = "✅" if results[name]['success'] else "❌"
                print(f"{status} {name}: {response.status_code} ({len(response.content)} bytes)")
                
                # 如果成功，尝试解析内容
                if response.status_code == 200:
                    content = response.text
                    
                    # 查找使用次数相关信息
                    usage_patterns = [
                        r'(\d+(?:\.\d+)?)\s+available',
                        r'Used\s+(\d+)\s+of\s+(\d+)',
                        r'(\d+)\s*/\s*(\d+)\s*(?:messages?|requests?)',
                        r'usage["\']?\s*:\s*(\d+)',
                        r'remaining["\']?\s*:\s*(\d+)',
                        r'limit["\']?\s*:\s*(\d+)'
                    ]
                    
                    found_info = []
                    for pattern in usage_patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        if matches:
                            found_info.append(f"模式 '{pattern}': {matches}")
                    
                    if found_info:
                        print(f"   📊 找到使用信息:")
                        for info in found_info:
                            print(f"      {info}")
                    else:
                        print(f"   ⚠️ 未找到使用次数信息")
                    
                    # 查找计划信息
                    plan_patterns = [
                        r'(Trial\s+Plan)',
                        r'(Pro\s+Plan)',
                        r'(Community\s+Plan)',
                        r'plan["\']?\s*:\s*["\']([^"\']+)["\']'
                    ]
                    
                    for pattern in plan_patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        if matches:
                            print(f"   📋 找到计划信息: {matches}")
                            break
                
            except Exception as e:
                results[name] = {'error': str(e)}
                print(f"❌ {name}: 访问失败 - {e}")
        
        return results
        
    except Exception as e:
        print(f"❌ 页面访问测试失败: {e}")
        return {}


def analyze_html_content():
    """分析HTML内容"""
    print("\n🔍 分析HTML内容...")
    
    try:
        from advanced_network_handler import get_network_handler
        from utils.simple_account_manager import get_current_login_info
        
        login_info = get_current_login_info()
        if not login_info:
            print("❌ 未找到登录信息")
            return
        
        handler = get_network_handler()
        handler.setup_cookies(login_info.get('cookie_string'))
        
        # 获取仪表板内容
        print("🔄 获取仪表板内容...")
        content = handler.get_page_content("/dashboard")
        
        if not content:
            print("❌ 无法获取仪表板内容")
            return
        
        print(f"✅ 获取到内容，长度: {len(content)} 字符")
        
        # 保存内容到文件以便分析
        with open("dashboard_content.html", "w", encoding="utf-8") as f:
            f.write(content)
        print("📄 内容已保存到 dashboard_content.html")
        
        # 分析内容
        print("\n🔍 内容分析:")
        
        # 查找标题
        title_match = re.search(r'<title>([^<]+)</title>', content, re.IGNORECASE)
        if title_match:
            print(f"📋 页面标题: {title_match.group(1)}")
        
        # 查找用户信息
        user_patterns = [
            r'Welcome,?\s+([^<,!]+)',
            r'Hello,?\s+([^<,!]+)',
            r'Hi,?\s+([^<,!]+)',
            r'email["\']?\s*:\s*["\']([^"\']+)["\']'
        ]
        
        for pattern in user_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                print(f"👤 用户信息: {matches}")
                break
        
        # 查找数字信息（可能是使用次数）
        number_patterns = [
            r'\b(\d+(?:\.\d+)?)\s+(?:available|remaining|left)\b',
            r'\bUsed\s+(\d+)\s+of\s+(\d+)\b',
            r'\b(\d+)\s*/\s*(\d+)\s*(?:messages?|requests?|uses?)\b',
            r'"usage":\s*(\d+)',
            r'"remaining":\s*(\d+)',
            r'"limit":\s*(\d+)'
        ]
        
        print("\n📊 数字信息分析:")
        for i, pattern in enumerate(number_patterns, 1):
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                print(f"   模式{i}: {matches}")
        
        # 查找JSON数据
        json_patterns = [
            r'window\.__INITIAL_STATE__\s*=\s*({.+?});',
            r'window\.APP_DATA\s*=\s*({.+?});',
            r'"subscription":\s*({.+?})',
            r'"user":\s*({.+?})'
        ]
        
        print("\n📦 JSON数据分析:")
        for i, pattern in enumerate(json_patterns, 1):
            matches = re.findall(pattern, content, re.DOTALL)
            if matches:
                print(f"   JSON模式{i}: 找到 {len(matches)} 个匹配")
                for j, match in enumerate(matches[:2]):  # 只显示前2个
                    try:
                        data = json.loads(match)
                        print(f"      匹配{j+1}: {json.dumps(data, indent=2)[:200]}...")
                    except:
                        print(f"      匹配{j+1}: {match[:100]}...")
        
        # 查找表格数据
        table_pattern = r'<table[^>]*>.*?</table>'
        tables = re.findall(table_pattern, content, re.DOTALL | re.IGNORECASE)
        if tables:
            print(f"\n📋 找到 {len(tables)} 个表格")
        
        # 查找列表数据
        list_pattern = r'<ul[^>]*>.*?</ul>'
        lists = re.findall(list_pattern, content, re.DOTALL | re.IGNORECASE)
        if lists:
            print(f"📝 找到 {len(lists)} 个列表")
        
    except Exception as e:
        print(f"❌ HTML内容分析失败: {e}")
        import traceback
        traceback.print_exc()


def test_api_endpoints():
    """测试API端点"""
    print("\n🔌 测试API端点...")
    
    try:
        from utils.simple_account_manager import get_current_login_info
        login_info = get_current_login_info()
        
        if not login_info:
            print("❌ 未找到登录信息")
            return
        
        cookie_string = login_info.get('cookie_string', '')
        
        # 解析Cookie
        cookies = {}
        if ';' in cookie_string:
            for item in cookie_string.split(';'):
                if '=' in item:
                    key, value = item.strip().split('=', 1)
                    cookies[key] = value
        
        session = requests.Session()
        for key, value in cookies.items():
            session.cookies.set(key, value)
        
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            'Referer': 'https://app.augmentcode.com/dashboard'
        })
        
        # 测试可能的API端点
        api_endpoints = [
            '/api/user',
            '/api/user/profile',
            '/api/user/subscription',
            '/api/subscription',
            '/api/usage',
            '/api/account',
            '/api/dashboard',
            '/api/me',
            '/user/me',
            '/subscription/current'
        ]
        
        base_url = "https://app.augmentcode.com"
        
        for endpoint in api_endpoints:
            try:
                url = base_url + endpoint
                print(f"🔄 测试API: {endpoint}")
                
                response = session.get(url, timeout=10)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"✅ {endpoint}: 成功获取JSON数据")
                        print(f"   数据: {json.dumps(data, indent=2)[:300]}...")
                    except:
                        print(f"✅ {endpoint}: 成功但非JSON数据 ({len(response.content)} bytes)")
                elif response.status_code == 404:
                    print(f"⚠️ {endpoint}: 端点不存在 (404)")
                elif response.status_code == 401:
                    print(f"❌ {endpoint}: 需要认证 (401)")
                elif response.status_code == 403:
                    print(f"❌ {endpoint}: 访问被拒绝 (403)")
                else:
                    print(f"⚠️ {endpoint}: 状态码 {response.status_code}")
                
            except Exception as e:
                print(f"❌ {endpoint}: 请求失败 - {e}")
        
    except Exception as e:
        print(f"❌ API端点测试失败: {e}")


def main():
    """主诊断函数"""
    print("🔧 AugmentCode账号信息获取诊断")
    print("=" * 60)
    print(f"📅 诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 执行诊断
    diagnose_cookie_validity()
    test_direct_page_access()
    analyze_html_content()
    test_api_endpoints()
    
    print("\n" + "=" * 60)
    print("🎯 诊断完成")
    print("\n💡 建议:")
    print("1. 检查 dashboard_content.html 文件中的具体内容")
    print("2. 如果找到了使用次数信息，可以优化解析规则")
    print("3. 如果没有找到，可能需要访问其他页面或API")
    print("4. 考虑检查网页的JavaScript代码中的数据")


if __name__ == "__main__":
    main()
