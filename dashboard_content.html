<!doctype html>
<html lang="en">
  <head>
    <title>Augment Login</title>
    <link rel="icon" href="/static/favicon.ico" />

    <style>
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background-color: #f8f9fa;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
      }

      .loading-container {
        text-align: center;
        padding: 2rem;
      }

      .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e3e3e3;
        border-top: 4px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .loading-text {
        color: #6c757d;
        font-size: 16px;
        margin-bottom: 0.5rem;
      }

      .loading-subtext {
        color: #9ca3af;
        font-size: 14px;
      }
    </style>

    <script src="https://cdn.verosint.com/verosintjs/0.2.11/verosintjs.umd.js"></script>
  </head>

  <body>
    <div class="loading-container">
      <div class="spinner"></div>
      <div class="loading-text">Redirecting to login...</div>
      <div class="loading-subtext">Please wait while we prepare your authentication</div>
      <a id="redirect_url" href="https://login.augmentcode.com/authorize?response_type=code&amp;client_id=wlLTVWGDfItW9HziIowSRieQNRylMPTa&amp;redirect_uri=https%3A%2F%2Fauth.augmentcode.com%2Foauth2%2Fcallback&amp;scope=openid+email+profile&amp;state=cmVzcG9uc2VfdHlwZT1jb2RlJmNsaWVudF9pZD1jdXN0b21lci11aSZyZWRpcmVjdF91cmk9aHR0cHMlM0ElMkYlMkZhcHAuYXVnbWVudGNvZGUuY29tJTJGYXV0aCUyRmNhbGxiYWNrJnN0YXRlPVJDdGc1ZGNkRXVzNk1BUklfOFFTaTlJWk53c3NHMTRlVkYwSnlrUXpYRHcmY29kZV9jaGFsbGVuZ2U9NVdvZ1d3SUJoX2E5MHFLLUl0WWlObjZiQkJnNkpxdHNiNjhqY3lkMk5rRSZjb2RlX2NoYWxsZW5nZV9tZXRob2Q9UzI1NiZ1PWFNZ3FLV0NlM2Uw&amp;prompt=login&amp;nonce=RexYH9ZiH2tHWU35YQhO" style="display: none;"></a>
    </div>
    <script>
        const redirectUrl = document.getElementById("redirect_url").getAttribute("href");

        VerosintJS.getDeviceId().then((id) => {
            window.location.href = redirectUrl + "&verosint_deviceid=" + id;
        });
        setTimeout(() => {
            window.location.href = redirectUrl;
        }, 3000);
    </script>
  </body>
</html>