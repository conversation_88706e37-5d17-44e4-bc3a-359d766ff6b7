#!/usr/bin/env python3
"""
工作区清理工具
清理VS Code工作区中的AugmentCode相关文件
"""

import os
import shutil
import json
from datetime import datetime

from utils.paths import get_vscode_path, get_storage_path, get_workspace_storage_path


def get_directory_size(path):
    """获取目录大小
    
    Args:
        path: 目录路径
    
    Returns:
        int: 目录大小（字节）
    """
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(path):
        for f in filenames:
            fp = os.path.join(dirpath, f)
            if os.path.exists(fp):
                try:
                    total_size += os.path.getsize(fp)
                except:
                    pass
    return total_size


def get_file_size(path):
    """获取文件大小
    
    Args:
        path: 文件路径
    
    Returns:
        int: 文件大小（字节）
    """
    if os.path.exists(path) and os.path.isfile(path):
        try:
            return os.path.getsize(path)
        except:
            pass
    return 0


def scan_workspace(custom_path=None):
    """扫描工作区
    
    Args:
        custom_path: 自定义路径，默认为None
    
    Returns:
        dict: 工作区扫描结果
    """
    if custom_path and os.path.exists(custom_path):
        base_path = custom_path
    else:
        base_path = get_vscode_path()
    
    items = []
    
    # 扫描全局存储
    storage_path = get_storage_path()
    if os.path.exists(storage_path):
        for publisher in os.listdir(storage_path):
            if "augment" in publisher.lower():
                publisher_path = os.path.join(storage_path, publisher)
                size = get_directory_size(publisher_path)
                items.append({
                    "path": publisher_path,
                    "type": "directory",
                    "description": "AugmentCode全局存储",
                    "size": size
                })
    
    # 扫描工作区存储
    workspace_storage = get_workspace_storage_path()
    if os.path.exists(workspace_storage):
        for workspace in os.listdir(workspace_storage):
            workspace_path = os.path.join(workspace_storage, workspace)
            if os.path.isdir(workspace_path):
                # 检查工作区中的扩展存储
                workspace_ext_path = os.path.join(workspace_path, "state")
                if os.path.exists(workspace_ext_path) and os.path.isdir(workspace_ext_path):
                    for ext in os.listdir(workspace_ext_path):
                        if "augment" in ext.lower():
                            ext_path = os.path.join(workspace_ext_path, ext)
                            size = get_directory_size(ext_path)
                            items.append({
                                "path": ext_path,
                                "type": "directory",
                                "description": "AugmentCode工作区存储",
                                "size": size
                            })
    
    # 扫描扩展目录
    extensions_path = os.path.join(os.path.expanduser("~"), ".vscode", "extensions")
    if os.path.exists(extensions_path):
        for ext in os.listdir(extensions_path):
            if "augment" in ext.lower():
                ext_path = os.path.join(extensions_path, ext)
                size = get_directory_size(ext_path)
                items.append({
                    "path": ext_path,
                    "type": "directory",
                    "description": "AugmentCode扩展",
                    "size": size
                })
    
    # 扫描用户设置
    user_settings_path = os.path.join(get_vscode_path(), "User", "settings.json")
    if os.path.exists(user_settings_path):
        try:
            with open(user_settings_path, 'r', encoding='utf-8') as f:
                settings = json.load(f)
                for key in list(settings.keys()):
                    if "augment" in key.lower():
                        items.append({
                            "path": user_settings_path,
                            "type": "file",
                            "description": "包含AugmentCode配置的用户设置",
                            "size": get_file_size(user_settings_path),
                            "setting_key": key
                        })
                        break
        except:
            pass
    
    # 扫描其他可能的文件
    other_paths = [
        os.path.join(get_vscode_path(), "User", "globalState.json"),
        os.path.join(get_vscode_path(), "User", "keybindings.json")
    ]
    
    for path in other_paths:
        if os.path.exists(path):
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "augment" in content.lower():
                        items.append({
                            "path": path,
                            "type": "file",
                            "description": "可能包含AugmentCode配置的文件",
                            "size": get_file_size(path)
                        })
            except:
                pass
    
    return {
        "scan_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "items": items
    }


def clean_workspace(items):
    """清理工作区
    
    Args:
        items: 要清理的项目列表
    
    Returns:
        dict: 清理结果
    """
    success_count = 0
    failed_count = 0
    cleaned_items = []
    failed_items = []
    
    for item in items:
        path = item.get('path')
        item_type = item.get('type')
        
        # 拷贝项目信息到结果中
        result_item = item.copy()
        
        try:
            if item_type == "directory":
                if os.path.exists(path) and os.path.isdir(path):
                    shutil.rmtree(path)
                    success_count += 1
                    result_item["status"] = "success"
                    cleaned_items.append(result_item)
                else:
                    failed_count += 1
                    result_item["status"] = "failed"
                    result_item["error"] = "目录不存在"
                    failed_items.append(result_item)
            
            elif item_type == "file":
                # 如果是设置文件，只删除特定的键
                if "setting_key" in item:
                    try:
                        with open(path, 'r', encoding='utf-8') as f:
                            settings = json.load(f)
                        
                        # 删除指定的键
                        if item["setting_key"] in settings:
                            del settings[item["setting_key"]]
                            
                            # 保存修改后的设置
                            with open(path, 'w', encoding='utf-8') as f:
                                json.dump(settings, f, indent=4)
                            
                            success_count += 1
                            result_item["status"] = "success"
                            cleaned_items.append(result_item)
                        else:
                            failed_count += 1
                            result_item["status"] = "failed"
                            result_item["error"] = "设置键不存在"
                            failed_items.append(result_item)
                    except Exception as e:
                        failed_count += 1
                        result_item["status"] = "failed"
                        result_item["error"] = str(e)
                        failed_items.append(result_item)
                
                # 否则直接删除文件
                else:
                    if os.path.exists(path) and os.path.isfile(path):
                        os.remove(path)
                        success_count += 1
                        result_item["status"] = "success"
                        cleaned_items.append(result_item)
                    else:
                        failed_count += 1
                        result_item["status"] = "failed"
                        result_item["error"] = "文件不存在"
                        failed_items.append(result_item)
        
        except Exception as e:
            failed_count += 1
            result_item["status"] = "failed"
            result_item["error"] = str(e)
            failed_items.append(result_item)
    
    return {
        "success_count": success_count,
        "failed_count": failed_count,
        "cleaned_items": cleaned_items,
        "failed_items": failed_items,
        "clean_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    } 