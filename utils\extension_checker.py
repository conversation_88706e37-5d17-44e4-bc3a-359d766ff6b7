import os
import json
import re
from pathlib import Path
from typing import Dict, List, Optional
from .paths import get_extensions_path


def get_augment_extension_info() -> Dict:
    """
    检查AugmentCode扩展的安装信息和版本
    
    Returns:
        dict: 包含扩展信息的字典
        {
            'installed': bool,
            'version': str,
            'path': str,
            'publisher': str,
            'display_name': str,
            'description': str,
            'enabled': bool,
            'install_date': str,
            'size': str
        }
    """
    extensions_path = get_extensions_path()
    
    result = {
        'installed': False,
        'version': 'N/A',
        'path': '',
        'publisher': 'N/A',
        'display_name': 'N/A',
        'description': 'N/A',
        'enabled': False,
        'install_date': 'N/A',
        'size': 'N/A'
    }
    
    if not os.path.exists(extensions_path):
        return result
    
    # 查找AugmentCode相关的扩展目录
    augment_dirs = []
    try:
        for item in os.listdir(extensions_path):
            if 'augment' in item.lower():
                full_path = os.path.join(extensions_path, item)
                if os.path.isdir(full_path):
                    augment_dirs.append(full_path)
    except (OSError, PermissionError):
        return result
    
    if not augment_dirs:
        return result
    
    # 选择最新的扩展目录（通常版本号在目录名中）
    latest_dir = max(augment_dirs, key=lambda x: os.path.getmtime(x))
    
    # 读取package.json文件获取扩展信息
    package_json_path = os.path.join(latest_dir, 'package.json')
    if os.path.exists(package_json_path):
        try:
            with open(package_json_path, 'r', encoding='utf-8') as f:
                package_data = json.load(f)
            
            result.update({
                'installed': True,
                'version': package_data.get('version', 'Unknown'),
                'path': latest_dir,
                'publisher': package_data.get('publisher', 'Unknown'),
                'display_name': package_data.get('displayName', package_data.get('name', 'AugmentCode')),
                'description': package_data.get('description', 'N/A')
            })
            
            # 获取安装日期
            try:
                install_time = os.path.getctime(latest_dir)
                from datetime import datetime
                result['install_date'] = datetime.fromtimestamp(install_time).strftime('%Y-%m-%d %H:%M:%S')
            except:
                result['install_date'] = 'Unknown'
            
            # 计算目录大小
            try:
                total_size = 0
                for dirpath, dirnames, filenames in os.walk(latest_dir):
                    for filename in filenames:
                        filepath = os.path.join(dirpath, filename)
                        try:
                            total_size += os.path.getsize(filepath)
                        except (OSError, FileNotFoundError):
                            continue
                
                # 转换为可读格式
                if total_size < 1024:
                    result['size'] = f"{total_size} B"
                elif total_size < 1024 * 1024:
                    result['size'] = f"{total_size / 1024:.1f} KB"
                else:
                    result['size'] = f"{total_size / (1024 * 1024):.1f} MB"
            except:
                result['size'] = 'Unknown'
                
        except (json.JSONDecodeError, OSError, PermissionError):
            result['installed'] = True
            result['path'] = latest_dir
    
    # 检查扩展是否启用（通过检查是否在禁用列表中）
    result['enabled'] = check_extension_enabled()
    
    return result


def check_extension_enabled() -> bool:
    """
    检查AugmentCode扩展是否启用
    
    Returns:
        bool: True表示启用，False表示禁用
    """
    try:
        from .paths import get_storage_path
        storage_path = get_storage_path()
        
        if not os.path.exists(storage_path):
            return True  # 默认认为是启用的
        
        with open(storage_path, 'r', encoding='utf-8') as f:
            storage_data = json.load(f)
        
        # 检查禁用的扩展列表
        disabled_extensions = storage_data.get('extensionsIdentifiers/disabled', [])
        
        # 查找AugmentCode相关的扩展ID
        for ext_id in disabled_extensions:
            if isinstance(ext_id, dict):
                ext_id = ext_id.get('id', '')
            if 'augment' in str(ext_id).lower():
                return False
        
        return True
        
    except (json.JSONDecodeError, OSError, PermissionError, KeyError):
        return True  # 出错时默认认为是启用的


def get_all_extensions_info() -> List[Dict]:
    """
    获取所有已安装扩展的信息
    
    Returns:
        list: 包含所有扩展信息的列表
    """
    extensions_path = get_extensions_path()
    extensions = []
    
    if not os.path.exists(extensions_path):
        return extensions
    
    try:
        for item in os.listdir(extensions_path):
            full_path = os.path.join(extensions_path, item)
            if os.path.isdir(full_path):
                package_json_path = os.path.join(full_path, 'package.json')
                if os.path.exists(package_json_path):
                    try:
                        with open(package_json_path, 'r', encoding='utf-8') as f:
                            package_data = json.load(f)
                        
                        extensions.append({
                            'name': package_data.get('name', item),
                            'display_name': package_data.get('displayName', package_data.get('name', item)),
                            'version': package_data.get('version', 'Unknown'),
                            'publisher': package_data.get('publisher', 'Unknown'),
                            'description': package_data.get('description', 'N/A'),
                            'path': full_path
                        })
                    except (json.JSONDecodeError, OSError):
                        continue
    except (OSError, PermissionError):
        pass
    
    return extensions


def find_augment_related_extensions() -> List[Dict]:
    """
    查找所有与AugmentCode相关的扩展
    
    Returns:
        list: 包含相关扩展信息的列表
    """
    all_extensions = get_all_extensions_info()
    augment_extensions = []
    
    keywords = ['augment', 'ai', 'code', 'assistant', 'copilot']
    
    for ext in all_extensions:
        name_lower = ext['name'].lower()
        display_name_lower = ext['display_name'].lower()
        description_lower = ext['description'].lower()
        
        # 检查是否包含关键词
        if any(keyword in name_lower or keyword in display_name_lower or keyword in description_lower 
               for keyword in keywords):
            augment_extensions.append(ext)
    
    return augment_extensions


def format_extension_info(ext_info: Dict) -> str:
    """
    格式化扩展信息为可读字符串
    
    Args:
        ext_info: 扩展信息字典
        
    Returns:
        str: 格式化的扩展信息
    """
    if not ext_info['installed']:
        return "❌ AugmentCode 扩展未安装"
    
    status = "✅ 启用" if ext_info['enabled'] else "⚠️ 禁用"
    
    info_lines = [
        f"📦 {ext_info['display_name']}",
        f"🏷️ 版本: {ext_info['version']}",
        f"👤 发布者: {ext_info['publisher']}",
        f"📝 描述: {ext_info['description'][:50]}{'...' if len(ext_info['description']) > 50 else ''}",
        f"📅 安装时间: {ext_info['install_date']}",
        f"📊 大小: {ext_info['size']}",
        f"🔧 状态: {status}",
        f"📁 路径: {ext_info['path']}"
    ]
    
    return "\n".join(info_lines)


if __name__ == "__main__":
    # 测试功能
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    from utils.paths import get_extensions_path

    print("🔍 检查 AugmentCode 扩展信息...")
    ext_info = get_augment_extension_info()
    print(format_extension_info(ext_info))

    print("\n" + "="*50)
    print("🔍 查找相关扩展...")
    related_exts = find_augment_related_extensions()
    for ext in related_exts:
        print(f"- {ext['display_name']} v{ext['version']} by {ext['publisher']}")
