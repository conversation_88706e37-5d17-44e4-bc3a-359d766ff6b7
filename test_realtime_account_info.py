#!/usr/bin/env python3
"""
测试实时账号信息获取功能
"""

import time
import json
from datetime import datetime

def test_enhanced_account_methods():
    """测试增强账号方法"""
    print("🧪 测试增强账号方法...")
    
    try:
        from enhanced_account_methods import get_enhanced_account_info
        
        # 获取当前登录信息
        from utils.simple_account_manager import get_current_login_info
        login_info = get_current_login_info()
        
        if not login_info:
            print("❌ 未找到登录信息，请先登录")
            return False
        
        cookie_string = login_info.get('cookie_string')
        if not cookie_string:
            print("❌ 未找到Cookie信息")
            return False
        
        print(f"📧 测试账号: {login_info.get('email', 'Unknown')}")
        print("🔄 正在获取增强账号信息...")
        
        # 测试增强方法
        success, account_info = get_enhanced_account_info(cookie_string)
        
        if success:
            print("✅ 增强账号信息获取成功！")
            print("📊 账号信息:")
            for key, value in account_info.items():
                if key not in ['cookie_string', 'parse_error', 'post_process_error']:
                    print(f"   {key}: {value}")
            return True
        else:
            print(f"❌ 增强账号信息获取失败: {account_info.get('error', 'Unknown error')}")
            return False
            
    except ImportError as e:
        print(f"❌ 导入增强账号方法失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试增强账号方法失败: {e}")
        return False


def test_realtime_refresher():
    """测试实时刷新器"""
    print("\n🧪 测试实时刷新器...")
    
    try:
        from utils.realtime_account_refresher import (
            get_realtime_refresher,
            add_account_info_callback,
            force_refresh_account_info
        )
        
        # 获取刷新器实例
        refresher = get_realtime_refresher()
        
        # 添加测试回调
        update_count = 0
        def test_callback(account_info):
            nonlocal update_count
            update_count += 1
            print(f"🔔 收到账号信息更新 #{update_count}")
            if account_info:
                email = account_info.get('email', 'Unknown')
                remaining = account_info.get('remaining_count', 'N/A')
                print(f"   📧 邮箱: {email}")
                print(f"   ⚡ 剩余次数: {remaining}")
                print(f"   🕒 更新时间: {account_info.get('refresh_time', 'N/A')}")
            else:
                print("   ❌ 无账号信息")
        
        add_account_info_callback(test_callback)
        
        # 启动刷新器
        refresher.start()
        print("✅ 实时刷新器已启动")
        
        # 强制刷新
        print("🔄 执行强制刷新...")
        force_refresh_account_info()
        
        # 等待一段时间观察结果
        print("⏳ 等待5秒观察刷新结果...")
        time.sleep(5)
        
        # 检查最新信息
        latest_info = refresher.get_latest_info()
        if latest_info:
            print("✅ 获取到最新账号信息:")
            print(f"   📧 邮箱: {latest_info.get('email', 'N/A')}")
            print(f"   📋 计划: {latest_info.get('plan_name', 'N/A')}")
            print(f"   ⚡ 剩余次数: {latest_info.get('remaining_count', 'N/A')}")
        else:
            print("❌ 未获取到最新账号信息")
        
        # 停止刷新器
        refresher.stop()
        print("✅ 实时刷新器已停止")
        
        return update_count > 0
        
    except Exception as e:
        print(f"❌ 测试实时刷新器失败: {e}")
        return False


def test_subscription_checker():
    """测试原有订阅检查器"""
    print("\n🧪 测试原有订阅检查器...")
    
    try:
        from utils.subscription_checker import quick_check_subscription
        from utils.simple_account_manager import get_current_login_info
        
        login_info = get_current_login_info()
        if not login_info:
            print("❌ 未找到登录信息")
            return False
        
        cookie_string = login_info.get('cookie_string')
        if not cookie_string:
            print("❌ 未找到Cookie信息")
            return False
        
        print("🔄 正在检查订阅信息...")
        success, sub_info = quick_check_subscription(cookie_string)
        
        if success:
            print("✅ 订阅信息获取成功！")
            print("📊 订阅信息:")
            for key, value in sub_info.items():
                if key not in ['cookie_string', 'error']:
                    print(f"   {key}: {value}")
            return True
        else:
            print(f"❌ 订阅信息获取失败: {sub_info.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试订阅检查器失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试实时账号信息获取功能")
    print("=" * 50)
    
    # 检查登录状态
    try:
        from utils.simple_account_manager import get_current_login_info
        login_info = get_current_login_info()
        
        if not login_info:
            print("❌ 未检测到登录信息，请先使用主程序登录账号")
            return
        
        print(f"✅ 检测到登录账号: {login_info.get('email', 'Unknown')}")
        print(f"🕒 最后使用时间: {login_info.get('last_used', 'Unknown')}")
        
    except Exception as e:
        print(f"❌ 检查登录状态失败: {e}")
        return
    
    # 执行测试
    test_results = []
    
    # 测试1: 原有订阅检查器
    result1 = test_subscription_checker()
    test_results.append(("原有订阅检查器", result1))
    
    # 测试2: 增强账号方法
    result2 = test_enhanced_account_methods()
    test_results.append(("增强账号方法", result2))
    
    # 测试3: 实时刷新器
    result3 = test_realtime_refresher()
    test_results.append(("实时刷新器", result3))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总:")
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    passed_count = sum(1 for _, result in test_results if result)
    total_count = len(test_results)
    
    print(f"\n🎯 总体结果: {passed_count}/{total_count} 项测试通过")
    
    if passed_count == total_count:
        print("🎉 所有测试通过！实时账号信息获取功能正常工作")
    elif passed_count > 0:
        print("⚠️ 部分测试通过，功能可能存在问题")
    else:
        print("❌ 所有测试失败，功能存在严重问题")


if __name__ == "__main__":
    main()
