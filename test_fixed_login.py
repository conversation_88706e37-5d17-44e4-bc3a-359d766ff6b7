#!/usr/bin/env python3
"""
测试修复后的登录功能
"""

from utils.simple_account_manager import save_login_info, get_current_login_info, clear_login_info

def test_fixed_login():
    """测试修复后的登录功能"""
    print("🔧 测试修复后的登录功能")
    print("=" * 50)
    
    # 1. 清除现有数据
    print("\n1. 清除现有数据:")
    clear_result = clear_login_info()
    print(f"清除结果: {'✅ 成功' if clear_result else '❌ 失败'}")
    
    # 2. 保存真实的测试账号信息
    print("\n2. 保存真实的测试账号信息:")
    test_email = "<EMAIL>"
    test_cookie = "session=real_session_123; user=real_user_456; auth=real_auth_789"
    test_username = "真实AugmentCode用户"
    
    save_result = save_login_info(test_email, test_cookie, test_username)
    print(f"保存结果: {'✅ 成功' if save_result else '❌ 失败'}")
    
    # 3. 验证保存的信息
    print("\n3. 验证保存的信息:")
    saved_info = get_current_login_info()
    if saved_info:
        print("✅ 验证成功:")
        print(f"   邮箱: {saved_info.get('email')}")
        print(f"   用户名: {saved_info.get('username')}")
        print(f"   Cookie长度: {len(saved_info.get('cookie_string', ''))} 字符")
        print(f"   最后使用: {saved_info.get('last_used')}")
    else:
        print("❌ 验证失败")
        return False
    
    # 4. 测试账号检测器
    print("\n4. 测试账号检测器:")
    try:
        from utils.account_detector import get_augment_account_info
        
        account_info = get_augment_account_info()
        print(f"登录状态: {'✅ 已登录' if account_info.get('logged_in') else '❌ 未登录'}")
        
        if account_info.get('logged_in'):
            print(f"邮箱: {account_info.get('email')}")
            print(f"用户名: {account_info.get('username')}")
            print(f"登录方式: {account_info.get('login_method')}")
            print(f"登录时间: {account_info.get('login_time')}")
            
            # 检查是否显示的是我们输入的真实信息
            if (account_info.get('email') == test_email and 
                account_info.get('username') == test_username):
                print("✅ 显示的是真实输入的信息，修复成功！")
                return True
            else:
                print("❌ 显示的不是真实输入的信息，还有问题")
                print(f"   期望邮箱: {test_email}")
                print(f"   实际邮箱: {account_info.get('email')}")
                print(f"   期望用户名: {test_username}")
                print(f"   实际用户名: {account_info.get('username')}")
                return False
        else:
            print("❌ 账号检测器显示未登录")
            return False
            
    except Exception as e:
        print(f"❌ 账号检测器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n" + "=" * 50)
    print("🎯 测试完成")

if __name__ == "__main__":
    success = test_fixed_login()
    
    if success:
        print("\n🎉 修复成功！现在GUI应该能正确显示您输入的真实账号信息了！")
        print("\n📋 下一步:")
        print("1. 重新启动GUI: python main.py")
        print("2. 进入'账号修改'页面")
        print("3. 输入您的真实AugmentCode账号信息")
        print("4. 保存后应该能看到正确的信息显示")
    else:
        print("\n❌ 还有问题需要进一步修复")
