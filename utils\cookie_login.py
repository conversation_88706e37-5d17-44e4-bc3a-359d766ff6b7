#!/usr/bin/env python3
"""
Cookie登录功能模块
处理用户输入的账号和Cookie信息进行登录验证
"""

import json
import os
import requests
import time
from typing import Dict, Optional, Tuple
from datetime import datetime, timedelta

class CookieLoginManager:
    """Cookie登录管理器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.login_info = {}
        
    def validate_cookie_format(self, cookie_string: str) -> bool:
        """
        验证Cookie格式是否正确（更宽松的验证）

        Args:
            cookie_string: Cookie字符串

        Returns:
            bool: Cookie格式是否有效
        """
        if not cookie_string or not isinstance(cookie_string, str):
            return False

        cookie_string = cookie_string.strip()
        if len(cookie_string) < 3:  # 至少需要 a=b 这样的格式
            return False

        try:
            # 更宽松的Cookie格式验证
            # 支持多种分隔符和格式

            # 如果包含分号，按分号分割
            if ';' in cookie_string:
                parts = cookie_string.split(';')
                valid_parts = 0
                for part in parts:
                    part = part.strip()
                    if part and ('=' in part or len(part) > 10):  # 要么有=号，要么是长字符串
                        valid_parts += 1
                return valid_parts > 0

            # 如果没有分号，检查是否是单个Cookie或长字符串
            elif '=' in cookie_string:
                return True  # 包含等号，可能是单个Cookie

            # 如果是长字符串（可能是token），也认为有效
            elif len(cookie_string) > 20:
                return True

            return False

        except Exception:
            # 如果有任何异常，但字符串足够长，也认为可能有效
            return len(cookie_string) > 10
    
    def parse_cookie_string(self, cookie_string: str) -> Dict[str, str]:
        """
        解析Cookie字符串为字典（支持多种格式）

        Args:
            cookie_string: Cookie字符串

        Returns:
            dict: 解析后的Cookie字典
        """
        cookies = {}
        if not cookie_string:
            return cookies

        try:
            cookie_string = cookie_string.strip()

            # 如果包含分号，按分号分割
            if ';' in cookie_string:
                parts = cookie_string.split(';')
                for i, part in enumerate(parts):
                    part = part.strip()
                    if '=' in part:
                        key, value = part.split('=', 1)
                        cookies[key.strip()] = value.strip()
                    elif part and len(part) > 5:
                        # 如果没有等号但是有内容，作为值处理
                        cookies[f'token_{i}'] = part

            # 如果没有分号但有等号
            elif '=' in cookie_string:
                key, value = cookie_string.split('=', 1)
                cookies[key.strip()] = value.strip()

            # 如果是纯字符串（可能是token）
            elif len(cookie_string) > 10:
                cookies['auth_token'] = cookie_string

        except Exception as e:
            print(f"解析Cookie失败: {e}")
            # 如果解析失败，但有内容，就作为单个token处理
            if cookie_string and len(cookie_string) > 5:
                cookies['raw_token'] = cookie_string

        return cookies
    
    def validate_augment_login(self, email: str, cookie_string: str) -> Tuple[bool, str, Dict]:
        """
        验证AugmentCode登录信息
        
        Args:
            email: 用户邮箱
            cookie_string: Cookie字符串
            
        Returns:
            tuple: (是否成功, 消息, 用户信息)
        """
        try:
            # 验证邮箱格式
            if not email or '@' not in email:
                return False, "邮箱格式无效", {}
            
            # 验证Cookie格式
            if not self.validate_cookie_format(cookie_string):
                return False, "Cookie格式无效", {}
            
            # 解析Cookie
            cookies = self.parse_cookie_string(cookie_string)
            if not cookies:
                return False, "Cookie解析失败", {}
            
            # 设置请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': 'https://augmentcode.com/',
                'Origin': 'https://augmentcode.com'
            }
            
            # 设置Cookie
            for key, value in cookies.items():
                self.session.cookies.set(key, value)
            
            # 模拟验证请求（这里可以根据实际的AugmentCode API调整）
            # 由于我们无法访问真实的AugmentCode API，这里进行基本验证
            
            # 检查Cookie是否包含有效内容（更宽松的验证）
            if len(cookies) == 0:
                return False, "Cookie解析后为空", {}

            # 检查是否有任何看起来像认证信息的Cookie
            auth_indicators = ['session', 'auth', 'token', 'user', 'login', 'access', 'jwt', 'bearer', 'sid', 'ssid']
            has_auth_like = any(
                any(indicator in key.lower() for indicator in auth_indicators)
                for key in cookies.keys()
            )

            # 如果没有明显的认证Cookie，但有其他Cookie，也允许通过（用户可能知道自己在做什么）
            if not has_auth_like and len(cookies) < 2:
                return False, "Cookie中可能缺少认证信息，请确保包含完整的登录Cookie", {}
            
            # 构造用户信息
            user_info = {
                'email': email,
                'username': email.split('@')[0],  # 从邮箱提取用户名
                'login_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'cookie_count': len(cookies),
                'cookies': cookies,
                'validated': True
            }
            
            return True, "登录验证成功", user_info
            
        except Exception as e:
            return False, f"验证过程出错: {str(e)}", {}
    
    def test_cookie_validity(self, cookies: Dict[str, str]) -> bool:
        """
        测试Cookie是否仍然有效
        
        Args:
            cookies: Cookie字典
            
        Returns:
            bool: Cookie是否有效
        """
        try:
            # 这里可以实现实际的Cookie有效性测试
            # 目前返回True表示假设Cookie有效
            return len(cookies) > 0
        except:
            return False
    
    def format_cookie_info(self, cookies: Dict[str, str]) -> str:
        """
        格式化Cookie信息用于显示
        
        Args:
            cookies: Cookie字典
            
        Returns:
            str: 格式化的Cookie信息
        """
        if not cookies:
            return "无Cookie信息"
        
        info_lines = []
        for key, value in cookies.items():
            # 隐藏敏感信息
            if len(value) > 20:
                display_value = value[:10] + "..." + value[-5:]
            else:
                display_value = value
            info_lines.append(f"  {key}: {display_value}")
        
        return f"Cookie信息 ({len(cookies)} 项):\n" + "\n".join(info_lines)


def create_cookie_login_manager() -> CookieLoginManager:
    """创建Cookie登录管理器实例"""
    return CookieLoginManager()


def quick_validate_login(email: str, cookie_string: str) -> Tuple[bool, str]:
    """
    快速验证登录信息
    
    Args:
        email: 邮箱
        cookie_string: Cookie字符串
        
    Returns:
        tuple: (是否成功, 消息)
    """
    manager = create_cookie_login_manager()
    success, message, _ = manager.validate_augment_login(email, cookie_string)
    return success, message
