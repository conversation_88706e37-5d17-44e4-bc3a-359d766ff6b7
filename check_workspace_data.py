#!/usr/bin/env python3
"""
检查工作区中的AugmentCode数据
"""

import sqlite3
import json
import os
import platform

def get_vscode_path():
    """获取VS Code配置路径"""
    if platform.system() == "Windows":
        return os.path.expandvars("%APPDATA%/Code")
    elif platform.system() == "Darwin":  # macOS
        return os.path.expanduser("~/Library/Application Support/Code")
    else:  # Linux
        return os.path.expanduser("~/.config/Code")

def check_workspace_data():
    """检查工作区中的AugmentCode数据"""
    vscode_path = get_vscode_path()
    workspace_id = '1904287da3976df484a4c6f91e1b413e'
    workspace_db = os.path.join(vscode_path, "User", "workspaceStorage", workspace_id, "state.vscdb")
    
    print(f"🔍 检查工作区数据: {workspace_id}")
    print("=" * 60)
    
    if os.path.exists(workspace_db):
        print(f"✅ 找到工作区数据库: {workspace_db}")
        
        try:
            conn = sqlite3.connect(workspace_db)
            cursor = conn.cursor()
            
            # 获取所有Augment相关的数据
            cursor.execute("SELECT key, value FROM ItemTable WHERE key LIKE '%augment%' OR key LIKE '%Augment%'")
            rows = cursor.fetchall()
            
            print(f"\n找到 {len(rows)} 条Augment相关记录:")
            
            for i, (key, value) in enumerate(rows):
                print(f"\n{i+1}. Key: {key}")
                
                # 尝试解析JSON
                try:
                    if value.startswith('{') or value.startswith('['):
                        data = json.loads(value)
                        print(f"   JSON数据:")
                        print(json.dumps(data, indent=4, ensure_ascii=False))
                    else:
                        print(f"   原始数据: {value}")
                except json.JSONDecodeError:
                    print(f"   原始数据: {value}")
                except Exception as e:
                    print(f"   解析错误: {e}")
                    print(f"   原始数据: {value}")
            
            # 查找可能包含用户信息的记录
            print(f"\n🔍 查找可能的用户信息:")
            cursor.execute("SELECT key, value FROM ItemTable WHERE value LIKE '%@%'")
            email_rows = cursor.fetchall()
            
            if email_rows:
                print(f"找到 {len(email_rows)} 条包含@符号的记录:")
                for key, value in email_rows:
                    print(f"   Key: {key}")
                    print(f"   Value: {value}")
                    print()
            else:
                print("未找到包含邮箱的记录")
            
            # 查找认证相关的记录
            print(f"\n🔍 查找认证相关记录:")
            auth_keywords = ['auth', 'token', 'session', 'user', 'login', 'account']
            for keyword in auth_keywords:
                cursor.execute(f"SELECT key, value FROM ItemTable WHERE key LIKE '%{keyword}%' OR value LIKE '%{keyword}%'")
                auth_rows = cursor.fetchall()
                
                if auth_rows:
                    print(f"关键词 '{keyword}' 相关记录 ({len(auth_rows)} 条):")
                    for key, value in auth_rows[:3]:  # 只显示前3条
                        print(f"   Key: {key}")
                        print(f"   Value: {value[:200]}...")
                        print()
            
            conn.close()
            
        except Exception as e:
            print(f"❌ 数据库查询失败: {e}")
    else:
        print(f"❌ 工作区数据库不存在: {workspace_db}")
    
    # 检查工作区的workspace.json文件
    workspace_json = os.path.join(vscode_path, "User", "workspaceStorage", workspace_id, "workspace.json")
    if os.path.exists(workspace_json):
        print(f"\n📄 检查workspace.json:")
        try:
            with open(workspace_json, 'r', encoding='utf-8') as f:
                workspace_data = json.load(f)
                print(json.dumps(workspace_data, indent=2, ensure_ascii=False))
        except Exception as e:
            print(f"❌ 读取workspace.json失败: {e}")
    else:
        print(f"\n❌ 未找到workspace.json: {workspace_json}")

if __name__ == "__main__":
    check_workspace_data()
