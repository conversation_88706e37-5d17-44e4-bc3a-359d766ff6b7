#!/usr/bin/env python3
"""
网络连接配置
提供各种网络优化策略
"""

import random
from typing import Dict, List


class NetworkConfig:
    """网络配置类"""
    
    # 用户代理池 - 模拟不同浏览器
    USER_AGENTS = [
        # Chrome Windows
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
        
        # Chrome Mac
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
        
        # Firefox
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0',
        
        # Edge
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0',
        
        # Safari
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15'
    ]
    
    # 请求头模板
    HEADER_TEMPLATES = {
        'chrome': {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        },
        'firefox': {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1'
        },
        'safari': {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
    }
    
    # 请求延迟配置
    DELAY_CONFIG = {
        'min_delay': 1.5,      # 最小延迟（秒）
        'max_delay': 4.0,      # 最大延迟（秒）
        'error_delay': 8.0,    # 错误后延迟（秒）
        'retry_delay': 15.0    # 重试延迟（秒）
    }
    
    # 超时配置
    TIMEOUT_CONFIG = {
        'connect_timeout': 10,  # 连接超时
        'read_timeout': 30,     # 读取超时
        'total_timeout': 45     # 总超时
    }
    
    # 重试配置
    RETRY_CONFIG = {
        'max_retries': 3,
        'backoff_factor': 1.5,
        'retry_status_codes': [429, 500, 502, 503, 504],
        'retry_methods': ['GET', 'HEAD', 'OPTIONS']
    }
    
    @classmethod
    def get_random_user_agent(cls) -> str:
        """获取随机用户代理"""
        return random.choice(cls.USER_AGENTS)
    
    @classmethod
    def get_browser_headers(cls, browser_type: str = None) -> Dict[str, str]:
        """获取浏览器请求头"""
        if browser_type is None:
            browser_type = random.choice(['chrome', 'firefox', 'safari'])
        
        headers = cls.HEADER_TEMPLATES.get(browser_type, cls.HEADER_TEMPLATES['chrome']).copy()
        
        # 添加随机用户代理
        user_agent = cls.get_random_user_agent()
        headers['User-Agent'] = user_agent
        
        # 根据用户代理调整其他头部
        if 'Firefox' in user_agent:
            headers.update(cls.HEADER_TEMPLATES['firefox'])
        elif 'Safari' in user_agent and 'Chrome' not in user_agent:
            headers.update(cls.HEADER_TEMPLATES['safari'])
        else:
            headers.update(cls.HEADER_TEMPLATES['chrome'])
        
        headers['User-Agent'] = user_agent  # 确保用户代理不被覆盖
        
        return headers
    
    @classmethod
    def get_random_delay(cls, delay_type: str = 'normal') -> float:
        """获取随机延迟"""
        if delay_type == 'error':
            return random.uniform(cls.DELAY_CONFIG['error_delay'], cls.DELAY_CONFIG['error_delay'] * 1.5)
        elif delay_type == 'retry':
            return random.uniform(cls.DELAY_CONFIG['retry_delay'], cls.DELAY_CONFIG['retry_delay'] * 1.3)
        else:
            return random.uniform(cls.DELAY_CONFIG['min_delay'], cls.DELAY_CONFIG['max_delay'])


class ProxyConfig:
    """代理配置类"""
    
    # 免费代理池（示例，实际使用时需要有效的代理）
    FREE_PROXIES = [
        # 格式: {'http': 'http://ip:port', 'https': 'https://ip:port'}
        # 注意：这些是示例代理，实际使用时需要替换为有效代理
    ]
    
    # 付费代理服务配置（示例）
    PREMIUM_PROXY_SERVICES = {
        'luminati': {
            'endpoint': 'zproxy.lum-superproxy.io',
            'port': 22225,
            'username': 'your_username',
            'password': 'your_password'
        },
        'smartproxy': {
            'endpoint': 'gate.smartproxy.com',
            'port': 10000,
            'username': 'your_username',
            'password': 'your_password'
        }
    }
    
    @classmethod
    def get_random_proxy(cls) -> Dict[str, str]:
        """获取随机代理"""
        if cls.FREE_PROXIES:
            return random.choice(cls.FREE_PROXIES)
        return {}
    
    @classmethod
    def format_proxy(cls, ip: str, port: int, username: str = None, password: str = None) -> Dict[str, str]:
        """格式化代理配置"""
        if username and password:
            proxy_url = f"http://{username}:{password}@{ip}:{port}"
        else:
            proxy_url = f"http://{ip}:{port}"
        
        return {
            'http': proxy_url,
            'https': proxy_url
        }


class AntiDetectionConfig:
    """反检测配置"""
    
    # 模拟真实浏览器行为的配置
    BEHAVIOR_CONFIG = {
        'enable_cookies': True,
        'enable_redirects': True,
        'max_redirects': 5,
        'enable_compression': True,
        'verify_ssl': True,
        'keep_alive': True
    }
    
    # 请求模式配置
    REQUEST_PATTERNS = {
        'burst_requests': False,    # 避免突发请求
        'random_intervals': True,   # 随机间隔
        'mimic_human': True,        # 模拟人类行为
        'vary_headers': True        # 变化请求头
    }
    
    @classmethod
    def should_rotate_headers(cls, request_count: int) -> bool:
        """判断是否应该轮换请求头"""
        return request_count % 10 == 0
    
    @classmethod
    def should_use_proxy(cls, error_count: int) -> bool:
        """判断是否应该使用代理"""
        return error_count >= 2
    
    @classmethod
    def get_error_handling_strategy(cls, error_type: str) -> Dict:
        """获取错误处理策略"""
        strategies = {
            'connection_reset': {
                'wait_time': random.uniform(10, 20),
                'recreate_session': True,
                'rotate_headers': True,
                'use_proxy': True
            },
            'timeout': {
                'wait_time': random.uniform(5, 10),
                'recreate_session': False,
                'rotate_headers': False,
                'use_proxy': False
            },
            'rate_limit': {
                'wait_time': random.uniform(30, 60),
                'recreate_session': True,
                'rotate_headers': True,
                'use_proxy': True
            },
            'forbidden': {
                'wait_time': random.uniform(60, 120),
                'recreate_session': True,
                'rotate_headers': True,
                'use_proxy': True
            }
        }
        
        return strategies.get(error_type, strategies['connection_reset'])


# 导出配置实例
network_config = NetworkConfig()
proxy_config = ProxyConfig()
anti_detection_config = AntiDetectionConfig()
